import { Timestamp } from "firebase/firestore";

/**
 * Types for admin management
 */

export interface AdminUser {
  id?: string;
  userId: string;
  email: string;
  displayName?: string;
  role: 'admin' | 'super-admin';
  permissions: AdminPermission[];
  isActive: boolean;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
  createdBy?: string;
}

export type AdminPermission =
  | 'manage-affiliate-links'
  | 'manage-users'
  | 'manage-builds'
  | 'view-analytics'
  | 'manage-admins'
  | 'full-access';

export interface AdminRole {
  name: string;
  permissions: AdminPermission[];
  description: string;
}

export const adminRoles: Record<string, AdminRole> = {
  admin: {
    name: 'Admin',
    permissions: ['manage-affiliate-links', 'view-analytics'],
    description: 'Can manage affiliate links and view analytics'
  },
  'super-admin': {
    name: 'Super Admin',
    permissions: ['full-access'],
    description: 'Full access to all admin features'
  }
};
