/* eslint-disable @typescript-eslint/no-unused-vars */
import { CountryCode, MarketplaceType } from '@/types/affiliate-links';

/**
 * Country data with names and regions
 */
export const countryData: Record<CountryCode, { name: string; currency: string; symbol: string; region: string }> = {
  // North America
  'US': { name: 'United States', currency: 'USD', symbol: '$', region: 'North America' },
  'CA': { name: 'Canada', currency: 'CAD', symbol: '$', region: 'North America' },
  'MX': { name: 'Mexico', currency: 'MXN', symbol: '$', region: 'North America' },

  // Europe
  'GB': { name: 'United Kingdom', currency: 'GBP', symbol: '£', region: 'Europe' },
  'DE': { name: 'Germany', currency: 'EUR', symbol: '€', region: 'Europe' },
  'FR': { name: 'France', currency: 'EUR', symbol: '€', region: 'Europe' },
  'ES': { name: 'Spain', currency: 'EUR', symbol: '€', region: 'Europe' },
  'IT': { name: 'Italy', currency: 'EUR', symbol: '€', region: 'Europe' },
  'NL': { name: 'Netherlands', currency: 'EUR', symbol: '€', region: 'Europe' },
  'SE': { name: 'Sweden', currency: 'SEK', symbol: 'kr', region: 'Europe' },
  'NO': { name: 'Norway', currency: 'NOK', symbol: 'kr', region: 'Europe' },
  'DK': { name: 'Denmark', currency: 'DKK', symbol: 'kr', region: 'Europe' },
  'FI': { name: 'Finland', currency: 'EUR', symbol: '€', region: 'Europe' },
  'PL': { name: 'Poland', currency: 'PLN', symbol: 'zł', region: 'Europe' },

  // Asia
  'IN': { name: 'India', currency: 'INR', symbol: '₹', region: 'Asia' },
  'CN': { name: 'China', currency: 'CNY', symbol: '¥', region: 'Asia' },
  'JP': { name: 'Japan', currency: 'JPY', symbol: '¥', region: 'Asia' },
  'KR': { name: 'South Korea', currency: 'KRW', symbol: '₩', region: 'Asia' },
  'SG': { name: 'Singapore', currency: 'SGD', symbol: '$', region: 'Asia' },
  'MY': { name: 'Malaysia', currency: 'MYR', symbol: 'RM', region: 'Asia' },
  'ID': { name: 'Indonesia', currency: 'IDR', symbol: 'Rp', region: 'Asia' },
  'TH': { name: 'Thailand', currency: 'THB', symbol: '฿', region: 'Asia' },
  'PH': { name: 'Philippines', currency: 'PHP', symbol: '₱', region: 'Asia' },
  'AE': { name: 'United Arab Emirates', currency: 'AED', symbol: 'د.إ', region: 'Asia' },
  'SA': { name: 'Saudi Arabia', currency: 'SAR', symbol: '﷼', region: 'Asia' },

  // Oceania
  'AU': { name: 'Australia', currency: 'AUD', symbol: '$', region: 'Oceania' },
  'NZ': { name: 'New Zealand', currency: 'NZD', symbol: '$', region: 'Oceania' },

  // South America
  'BR': { name: 'Brazil', currency: 'BRL', symbol: 'R$', region: 'South America' },
  'AR': { name: 'Argentina', currency: 'ARS', symbol: '$', region: 'South America' },
  'CL': { name: 'Chile', currency: 'CLP', symbol: '$', region: 'South America' },
  'CO': { name: 'Colombia', currency: 'COP', symbol: '$', region: 'South America' },
  'PE': { name: 'Peru', currency: 'PEN', symbol: 'S/.', region: 'South America' },

  // Africa
  'ZA': { name: 'South Africa', currency: 'ZAR', symbol: 'R', region: 'Africa' },
  'NG': { name: 'Nigeria', currency: 'NGN', symbol: '₦', region: 'Africa' },
  'EG': { name: 'Egypt', currency: 'EGP', symbol: '£', region: 'Africa' },
  'KE': { name: 'Kenya', currency: 'KES', symbol: 'KSh', region: 'Africa' }
};

/**
 * Marketplace data with names and regions
 */
export const marketplaceData: Record<MarketplaceType, { name: string; regions: string[] }> = {
  // Global marketplaces
  'amazon': {
    name: 'Amazon',
    regions: ['North America', 'Europe', 'Asia', 'Oceania', 'South America', 'Africa']
  },
  'newegg': {
    name: 'Newegg',
    regions: ['North America', 'Europe', 'Asia', 'Oceania', 'South America']
  },
  'walmart': {
    name: 'Walmart',
    regions: ['North America']
  },
  'bestbuy': {
    name: 'Best Buy',
    regions: ['North America']
  },

  // Regional marketplaces - North America
  'microcenter': {
    name: 'Micro Center',
    regions: ['North America']
  },
  'bhphotovideo': {
    name: 'B&H Photo Video',
    regions: ['North America']
  },
  'canadacomputers': {
    name: 'Canada Computers',
    regions: ['North America']
  },
  'memoryexpress': {
    name: 'Memory Express',
    regions: ['North America']
  },
  'cyberpuerta': {
    name: 'CyberPuerta',
    regions: ['North America']
  },

  // Regional marketplaces - Europe
  'scan': {
    name: 'Scan',
    regions: ['Europe']
  },
  'overclockers': {
    name: 'Overclockers',
    regions: ['Europe']
  },
  'ebuyer': {
    name: 'Ebuyer',
    regions: ['Europe']
  },
  'alternate': {
    name: 'Alternate',
    regions: ['Europe']
  },
  'mindfactory': {
    name: 'Mindfactory',
    regions: ['Europe']
  },
  'caseking': {
    name: 'Caseking',
    regions: ['Europe']
  },
  'ldlc': {
    name: 'LDLC',
    regions: ['Europe']
  },
  'materiel': {
    name: 'Materiel.net',
    regions: ['Europe']
  },
  'pccomponentes': {
    name: 'PcComponentes',
    regions: ['Europe']
  },
  'mediamarkt': {
    name: 'MediaMarkt',
    regions: ['Europe']
  },

  // Regional marketplaces - Asia
  'flipkart': {
    name: 'Flipkart',
    regions: ['Asia']
  },
  'jd': {
    name: 'JD.com',
    regions: ['Asia']
  },
  'rakuten': {
    name: 'Rakuten',
    regions: ['Asia']
  },
  'coupang': {
    name: 'Coupang',
    regions: ['Asia']
  },
  'lazada': {
    name: 'Lazada',
    regions: ['Asia']
  },
  'shopee': {
    name: 'Shopee',
    regions: ['Asia']
  },

  // Regional marketplaces - Oceania
  'mwave': {
    name: 'Mwave',
    regions: ['Oceania']
  },
  'pccasegear': {
    name: 'PC Case Gear',
    regions: ['Oceania']
  },
  'pbtech': {
    name: 'PB Tech',
    regions: ['Oceania']
  },

  // Regional marketplaces - South America
  'mercadolibre': {
    name: 'MercadoLibre',
    regions: ['South America']
  },
  'kabum': {
    name: 'KaBuM!',
    regions: ['South America']
  },

  // Regional marketplaces - Africa
  'takealot': {
    name: 'Takealot',
    regions: ['Africa']
  },

  // Other
  'other': {
    name: 'Other',
    regions: ['North America', 'Europe', 'Asia', 'Oceania', 'South America', 'Africa']
  }
};

/**
 * Get country name from country code
 * @param code Country code
 * @returns Country name
 */
export function getCountryName(code: CountryCode): string {
  return countryData[code]?.name || code;
}

/**
 * Get marketplace name from marketplace type
 * @param marketplace Marketplace type
 * @returns Marketplace name
 */
export function getMarketplaceName(marketplace: MarketplaceType): string {
  return marketplaceData[marketplace]?.name || marketplace;
}

/**
 * Get countries by region
 * @param region Region name
 * @returns Array of country codes in the region
 */
export function getCountriesByRegion(region: string): CountryCode[] {
  return Object.entries(countryData)
    .filter(([_, data]) => data.region === region)
    .map(([code]) => code as CountryCode);
}

/**
 * Get marketplaces by region
 * @param region Region name
 * @returns Array of marketplace types available in the region
 */
export function getMarketplacesByRegion(region: string): MarketplaceType[] {
  return Object.entries(marketplaceData)
    .filter(([_, data]) => data.regions.includes(region))
    .map(([marketplace]) => marketplace as MarketplaceType);
}

/**
 * Get all regions
 * @returns Array of unique region names
 */
export function getAllRegions(): string[] {
  return [...new Set(Object.values(countryData).map(data => data.region))];
}

/**
 * Country-specific marketplace domains
 * Maps country codes to their marketplace domains
 */
export const countryMarketplaceDomains: Record<CountryCode, Record<string, string>> = {
  // North America
  'US': {
    amazon: 'amazon.com',
    newegg: 'newegg.com',
    walmart: 'walmart.com',
    bestbuy: 'bestbuy.com',
    microcenter: 'microcenter.com',
    bhphotovideo: 'bhphotovideo.com'
  },
  'CA': {
    amazon: 'amazon.ca',
    newegg: 'newegg.ca',
    walmart: 'walmart.ca',
    bestbuy: 'bestbuy.ca',
    canadacomputers: 'canadacomputers.com',
    memoryexpress: 'memoryexpress.com'
  },
  'MX': {
    amazon: 'amazon.com.mx',
    walmart: 'walmart.com.mx',
    cyberpuerta: 'cyberpuerta.mx'
  },

  // Europe
  'GB': {
    amazon: 'amazon.co.uk',
    newegg: 'newegg.com',
    scan: 'scan.co.uk',
    overclockers: 'overclockers.co.uk',
    ebuyer: 'ebuyer.com'
  },
  'DE': {
    amazon: 'amazon.de',
    newegg: 'newegg.com',
    alternate: 'alternate.de',
    mindfactory: 'mindfactory.de',
    caseking: 'caseking.de',
    mediamarkt: 'mediamarkt.de'
  },
  'FR': {
    amazon: 'amazon.fr',
    newegg: 'newegg.com',
    ldlc: 'ldlc.com',
    materiel: 'materiel.net'
  },
  'ES': {
    amazon: 'amazon.es',
    newegg: 'newegg.com',
    pccomponentes: 'pccomponentes.com',
    mediamarkt: 'mediamarkt.es'
  },
  'IT': {
    amazon: 'amazon.it',
    newegg: 'newegg.com',
    mediamarkt: 'mediamarkt.it'
  },
  'NL': {
    amazon: 'amazon.nl',
    newegg: 'newegg.com',
    alternate: 'alternate.nl',
    mediamarkt: 'mediamarkt.nl'
  },
  'SE': {
    amazon: 'amazon.se',
    newegg: 'newegg.com'
  },
  'NO': {
    amazon: 'amazon.no',
    newegg: 'newegg.com'
  },
  'DK': {
    amazon: 'amazon.dk',
    newegg: 'newegg.com'
  },
  'FI': {
    amazon: 'amazon.fi',
    newegg: 'newegg.com'
  },
  'PL': {
    amazon: 'amazon.pl',
    newegg: 'newegg.com'
  },

  // Asia
  'IN': {
    amazon: 'amazon.in',
    flipkart: 'flipkart.com'
  },
  'CN': {
    amazon: 'amazon.cn',
    jd: 'jd.com'
  },
  'JP': {
    amazon: 'amazon.co.jp',
    rakuten: 'rakuten.co.jp'
  },
  'KR': {
    amazon: 'amazon.kr',
    coupang: 'coupang.com'
  },
  'SG': {
    amazon: 'amazon.sg',
    lazada: 'lazada.sg',
    shopee: 'shopee.sg'
  },
  'MY': {
    amazon: 'amazon.my',
    lazada: 'lazada.com.my',
    shopee: 'shopee.com.my'
  },
  'ID': {
    amazon: 'amazon.id',
    lazada: 'lazada.co.id',
    shopee: 'shopee.co.id'
  },
  'TH': {
    amazon: 'amazon.th',
    lazada: 'lazada.co.th',
    shopee: 'shopee.co.th'
  },
  'PH': {
    amazon: 'amazon.ph',
    lazada: 'lazada.com.ph',
    shopee: 'shopee.ph'
  },
  'AE': {
    amazon: 'amazon.ae'
  },
  'SA': {
    amazon: 'amazon.sa'
  },

  // Oceania
  'AU': {
    amazon: 'amazon.com.au',
    newegg: 'newegg.com.au',
    mwave: 'mwave.com.au',
    pccasegear: 'pccasegear.com'
  },
  'NZ': {
    amazon: 'amazon.nz',
    pbtech: 'pbtech.co.nz'
  },

  // South America
  'BR': {
    amazon: 'amazon.com.br',
    mercadolibre: 'mercadolivre.com.br',
    kabum: 'kabum.com.br'
  },
  'AR': {
    amazon: 'amazon.com.ar',
    mercadolibre: 'mercadolibre.com.ar'
  },
  'CL': {
    amazon: 'amazon.cl',
    mercadolibre: 'mercadolibre.cl'
  },
  'CO': {
    amazon: 'amazon.co.co',
    mercadolibre: 'mercadolibre.com.co'
  },
  'PE': {
    amazon: 'amazon.pe',
    mercadolibre: 'mercadolibre.com.pe'
  },

  // Africa
  'ZA': {
    amazon: 'amazon.za',
    takealot: 'takealot.com'
  },
  'NG': {
    amazon: 'amazon.ng'
  },
  'EG': {
    amazon: 'amazon.eg'
  },
  'KE': {
    amazon: 'amazon.ke'
  }
};

/**
 * Search URL patterns for different marketplaces
 */
export const searchUrlPatterns: Record<string, string> = {
  amazon: '/s?k=',
  newegg: '/p/pl?d=',
  walmart: '/search?q=',
  bestbuy: '/site/searchpage.jsp?st=',
  microcenter: '/search/search_results.aspx?Ntk=all&sortby=match&N=4294966995&myStore=false&Ntt=',
  bhphotovideo: '/c/search?Ntt=',
  canadacomputers: '/search/?ccid=',
  memoryexpress: '/Search/Products?Search=',
  cyberpuerta: '/buscar/',
  scan: '/search?q=',
  overclockers: '/search?sSearch=',
  ebuyer: '/search?q=',
  alternate: '/search/?q=',
  mindfactory: '/search?sSearch=',
  caseking: '/search?sSearch=',
  ldlc: '/recherche/',
  materiel: '/recherche/',
  pccomponentes: '/buscar/?query=',
  mediamarkt: '/search?query=',
  flipkart: '/search?q=',
  jd: '/search?keyword=',
  rakuten: '/search/',
  coupang: '/np/search?q=',
  lazada: '/catalog/?q=',
  shopee: '/search?keyword=',
  mwave: '/search?search=',
  pccasegear: '/search?query=',
  pbtech: '/search?query=',
  mercadolibre: '/jm/search?as_word=',
  mercadolivre: '/jm/search?as_word=',
  kabum: '/busca/',
  takealot: '/all/search?qsearch='
};

/**
 * Generate search URL for a component in a specific country and marketplace
 * @param countryCode Country code
 * @param marketplace Marketplace type
 * @param componentName Component name to search for
 * @returns Search URL
 */
export function generateSearchUrl(
  countryCode: CountryCode,
  marketplace: MarketplaceType,
  componentName: string
): string {
  const countryDomains = countryMarketplaceDomains[countryCode];
  const domain = countryDomains?.[marketplace];

  if (!domain) {
    // Fallback to US domain for unsupported country/marketplace combinations
    const fallbackDomain = countryMarketplaceDomains['US'][marketplace] || 'amazon.com';
    const searchPattern = searchUrlPatterns[marketplace] || '/s?k=';
    return `https://${fallbackDomain}${searchPattern}${encodeURIComponent(componentName)}`;
  }

  const searchPattern = searchUrlPatterns[marketplace] || '/s?k=';
  return `https://${domain}${searchPattern}${encodeURIComponent(componentName)}`;
}

/**
 * Get available marketplaces for a country
 * @param countryCode Country code
 * @returns Array of available marketplace types
 */
export function getAvailableMarketplacesForCountry(countryCode: CountryCode): MarketplaceType[] {
  const countryDomains = countryMarketplaceDomains[countryCode];
  if (!countryDomains) {
    return ['amazon', 'newegg']; // Default fallback
  }

  return Object.keys(countryDomains) as MarketplaceType[];
}
