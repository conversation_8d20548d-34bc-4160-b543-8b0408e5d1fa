'use client';

import { VertexAIService } from './vertex-ai-service';

export class ComponentExplanationService {
  /**
   * Get explanation for why a specific component was chosen
   */
  static async explainComponentChoice(
    componentType: string,
    componentName: string,
    buildContext: {
      budget: number;
      useCase: string[];
      preferences?: Record<string, string | number | boolean>;
    }
  ): Promise<string> {
    try {
      const prompt = `
You are a PC building expert. Explain why the ${componentName} (${componentType}) was chosen for this specific build.

Build Context:
- Budget: $${buildContext.budget}
- Use Case: ${buildContext.useCase.join(', ')}
- Preferences: ${JSON.stringify(buildContext.preferences || {})}

Component: ${componentName} (${componentType})

Please provide a clear, concise explanation (2-3 sentences) covering:
1. Why this specific component fits the budget and use case
2. What key features or specifications make it suitable
3. How it compares to alternatives in this price range

Keep the explanation accessible to both beginners and experienced builders.
`;

      const response = await VertexAIService.generateText(prompt);
      return response;
    } catch (error) {
      console.error('Error generating component explanation:', error);

      // Fallback explanation based on component type
      return this.getFallbackExplanation(componentType, componentName, buildContext);
    }
  }

  /**
   * Fallback explanation when AI service fails
   */
  private static getFallbackExplanation(
    componentType: string,
    componentName: string,
    buildContext: { budget: number; useCase: string[] }
  ): string {
    const useCaseText = buildContext.useCase.join(' and ');

    switch (componentType.toLowerCase()) {
      case 'cpu':
      case 'processor':
        return `The ${componentName} was selected for its excellent performance in ${useCaseText} applications within your $${buildContext.budget} budget. This processor offers the right balance of cores, clock speed, and efficiency for your specific needs.`;

      case 'gpu':
      case 'graphics card':
        return `The ${componentName} provides optimal graphics performance for ${useCaseText} at your budget level of $${buildContext.budget}. This GPU delivers the frame rates and visual quality you need while staying within budget.`;

      case 'motherboard':
        return `The ${componentName} motherboard was chosen for its compatibility with your selected components and feature set that supports ${useCaseText}. It provides the necessary connectivity and expansion options within your $${buildContext.budget} budget.`;

      case 'ram':
      case 'memory':
        return `The ${componentName} memory configuration provides sufficient capacity and speed for ${useCaseText} workloads. This RAM amount and speed offer the best performance per dollar at your $${buildContext.budget} budget level.`;

      case 'storage':
      case 'ssd':
      case 'hdd':
        return `The ${componentName} storage solution balances capacity, speed, and cost for ${useCaseText} applications. This drive provides the storage performance and space you need within your $${buildContext.budget} budget.`;

      case 'psu':
      case 'power supply':
        return `The ${componentName} power supply provides reliable, efficient power delivery for your system. It offers the right wattage and efficiency rating for ${useCaseText} while fitting your $${buildContext.budget} budget.`;

      case 'case':
        return `The ${componentName} case was selected for its size, airflow, and build quality that suits ${useCaseText} systems. It provides proper component clearance and cooling within your $${buildContext.budget} budget.`;

      case 'cooler':
      case 'cpu cooler':
        return `The ${componentName} cooler ensures optimal CPU temperatures for ${useCaseText} workloads. This cooling solution provides the thermal performance needed while staying within your $${buildContext.budget} budget.`;

      default:
        return `The ${componentName} was carefully selected to complement your ${useCaseText} build requirements. This ${componentType} offers excellent value and performance within your $${buildContext.budget} budget range.`;
    }
  }

  /**
   * Get general build explanation
   */
  static async explainBuildChoices(
    buildName: string,
    components: Array<{ type: string; name: string; price: string }>,
    buildContext: {
      budget: number;
      useCase: string[];
      preferences?: Record<string, string | number | boolean>;
    }
  ): Promise<string> {
    try {
      const componentList = components.map(c => `${c.type}: ${c.name} ($${c.price})`).join('\n');

      const prompt = `
You are a PC building expert. Explain the overall component choices for this PC build.

Build: ${buildName}
Budget: $${buildContext.budget}
Use Case: ${buildContext.useCase.join(', ')}
Preferences: ${JSON.stringify(buildContext.preferences || {})}

Components:
${componentList}

Provide a comprehensive explanation (3-4 sentences) covering:
1. How the components work together for the intended use case
2. The balance between performance and budget
3. Any notable features or advantages of this configuration
4. Why this combination is optimal for the user's needs

Keep the explanation informative but accessible.
`;

      const response = await VertexAIService.generateText(prompt);
      return response;
    } catch (error) {
      console.error('Error generating build explanation:', error);

      const useCaseText = buildContext.useCase.join(' and ');
      return `This ${buildName} build is carefully balanced for ${useCaseText} within your $${buildContext.budget} budget. Each component was selected to work harmoniously together, providing optimal performance while maintaining cost efficiency. The configuration ensures you get the best possible experience for your intended use case without overspending on unnecessary features.`;
    }
  }
}
