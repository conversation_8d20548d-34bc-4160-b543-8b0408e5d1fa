'use client';

import { FirestoreService } from './firestore-service';
import { AdminUser, AdminPermission } from '@/types/admin';

/**
 * Admin Service
 * Handles admin user management and permissions
 */
export class AdminService {
  private static readonly COLLECTION_NAME = 'admin-users';

  /**
   * Check if a user is an admin
   * @param userId User ID to check
   * @returns Promise with admin status
   */
  static async isAdmin(userId: string): Promise<boolean> {
    try {
      const adminUsers = await FirestoreService.queryDocuments(
        this.COLLECTION_NAME,
        [
          { fieldPath: 'userId', opStr: '==', value: userId },
          { fieldPath: 'isActive', opStr: '==', value: true }
        ]
      ) as AdminUser[];

      return adminUsers.length > 0;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  }

  /**
   * Get admin user data
   * @param userId User ID
   * @returns Promise with admin user data or null
   */
  static async getAdminUser(userId: string): Promise<AdminUser | null> {
    try {
      const adminUsers = await FirestoreService.queryDocuments(
        this.COLLECTION_NAME,
        [
          { fieldPath: 'userId', opStr: '==', value: userId },
          { fieldPath: 'isActive', opStr: '==', value: true }
        ]
      ) as AdminUser[];

      return adminUsers.length > 0 ? adminUsers[0] : null;
    } catch (error) {
      console.error('Error getting admin user:', error);
      return null;
    }
  }

  /**
   * Check if user has specific permission
   * @param userId User ID
   * @param permission Permission to check
   * @returns Promise with permission status
   */
  static async hasPermission(userId: string, permission: AdminPermission): Promise<boolean> {
    try {
      const adminUser = await this.getAdminUser(userId);

      if (!adminUser) {
        return false;
      }

      // Super admin or full-access has all permissions
      if (adminUser.permissions.includes('full-access')) {
        return true;
      }

      return adminUser.permissions.includes(permission);
    } catch (error) {
      console.error('Error checking permission:', error);
      return false;
    }
  }

  /**
   * Add a new admin user
   * @param adminData Admin user data
   * @param createdBy User ID of the admin creating this user
   * @returns Promise with created admin user ID
   */
  static async addAdminUser(adminData: Omit<AdminUser, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, createdBy: string): Promise<string> {
    try {
      // Check if user is already an admin
      const existingAdmin = await this.getAdminUser(adminData.userId);
      if (existingAdmin) {
        throw new Error('User is already an admin');
      }

      const docRef = await FirestoreService.addDocument(this.COLLECTION_NAME, {
        ...adminData,
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy
      });

      return docRef.id;
    } catch (error) {
      console.error('Error adding admin user:', error);
      throw error;
    }
  }

  /**
   * Update admin user
   * @param adminId Admin user document ID
   * @param updateData Data to update
   * @returns Promise that resolves when update is complete
   */
  static async updateAdminUser(adminId: string, updateData: Partial<AdminUser>): Promise<void> {
    try {
      await FirestoreService.updateDocument(this.COLLECTION_NAME, adminId, {
        ...updateData,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error updating admin user:', error);
      throw error;
    }
  }

  /**
   * Remove admin user (deactivate)
   * @param adminId Admin user document ID
   * @returns Promise that resolves when removal is complete
   */
  static async removeAdminUser(adminId: string): Promise<void> {
    try {
      await FirestoreService.updateDocument(this.COLLECTION_NAME, adminId, {
        isActive: false,
        updatedAt: new Date()
      });
    } catch (error) {
      console.error('Error removing admin user:', error);
      throw error;
    }
  }

  /**
   * Get all admin users
   * @returns Promise with array of admin users
   */
  static async getAllAdminUsers(): Promise<AdminUser[]> {
    try {
      const adminUsers = await FirestoreService.queryDocuments(
        this.COLLECTION_NAME,
        []
      ) as AdminUser[];

      return adminUsers.sort((a, b) => {
        // Sort by creation date, newest first
        if (a?.createdAt && b?.createdAt) {
          return b.createdAt.seconds - a.createdAt.seconds;
        }
        return 0;
      });
    } catch (error) {
      console.error('Error getting all admin users:', error);
      return [];
    }
  }

  /**
   * Get active admin users
   * @returns Promise with array of active admin users
   */
  static async getActiveAdminUsers(): Promise<AdminUser[]> {
    try {
      const adminUsers = await FirestoreService.queryDocuments(
        this.COLLECTION_NAME,
        [{ fieldPath: 'isActive', opStr: '==', value: true }]
      ) as AdminUser[];

      return adminUsers.sort((a, b) => {
        // Sort by creation date, newest first
        if (a?.createdAt && b?.createdAt) {
          return b.createdAt.seconds - a.createdAt.seconds;
        }
        return 0;
      });
    } catch (error) {
      console.error('Error getting active admin users:', error);
      return [];
    }
  }
}

export default AdminService;
