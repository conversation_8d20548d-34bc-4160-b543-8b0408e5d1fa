'use client';

import { User } from 'firebase/auth';
import { FirestoreService } from './firestore-service';
import { UserProfile } from '@/lib/store/auth-store';

/**
 * User Service
 * Provides methods for user profile management
 */
export class UserService {
  private static readonly COLLECTION_NAME = 'users';

  /**
   * Create or update a user profile
   * @param userId User ID
   * @param profileData User profile data
   * @returns Promise that resolves when profile is created/updated
   */
  static async createOrUpdateProfile(userId: string, profileData: Partial<UserProfile>) {
    try {
      // Check if user profile exists
      const userProfiles = await FirestoreService.queryDocuments(
        this.COLLECTION_NAME,
        [{ fieldPath: 'userId', opStr: '==', value: userId }]
      );

      if (userProfiles.length > 0) {
        // Update existing profile
        const profileId = userProfiles[0].id as string;
        await FirestoreService.updateDocument(this.COLLECTION_NAME, profileId, {
          ...profileData,
          updatedAt: new Date()
        });
        return { id: profileId, ...profileData };
      } else {
        // Create new profile
        const docRef = await FirestoreService.addDocument(this.COLLECTION_NAME, {
          userId,
          ...profileData,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        return { id: docRef.id, ...profileData };
      }
    } catch (error) {
      console.error("Error creating/updating user profile:", error);
      throw error;
    }
  }

  /**
   * Get a user profile by user ID
   * @param userId User ID
   * @returns Promise with user profile data
   */
  static async getProfileByUserId(userId: string) {
    try {
      const userProfiles = await FirestoreService.queryDocuments(
        this.COLLECTION_NAME,
        [{ fieldPath: 'userId', opStr: '==', value: userId }]
      );

      if (userProfiles.length > 0) {
        return userProfiles[0] as UserProfile & { id: string };
      }

      return null;
    } catch (error) {
      console.error("Error getting user profile:", error);
      throw error;
    }
  }

  /**
   * Get a user profile by email
   * @param email User email
   * @returns Promise with user profile data
   */
  static async getProfileByEmail(email: string) {
    try {
      const userProfiles = await FirestoreService.queryDocuments(
        this.COLLECTION_NAME,
        [{ fieldPath: 'email', opStr: '==', value: email }]
      );

      if (userProfiles.length > 0) {
        return userProfiles[0] as UserProfile & { id: string, userId: string };
      }

      return null;
    } catch (error) {
      console.error("Error getting user profile:", error);
      throw error;
    }
  }

  /**
   * Create a user profile from Firebase Auth user
   * @param user Firebase Auth user
   * @returns Promise with user profile data
   */
  static async createProfileFromAuthUser(user: User, additionalData?: Partial<UserProfile>) {
    try {
      // Extract name parts from displayName if available
      let firstName = '';
      let lastName = '';

      if (user.displayName) {
        const nameParts = user.displayName.split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
      }

      const profileData: Partial<UserProfile> = {
        firstName,
        lastName,
        email: user.email || '',
        photoURL: user.photoURL || undefined,
        ...additionalData
      };

      return await this.createOrUpdateProfile(user.uid, profileData);
    } catch (error) {
      console.error("Error creating profile from auth user:", error);
      throw error;
    }
  }
}

export default UserService;
