'use client';

import { FirestoreService } from './firestore-service';
import { DocumentData } from 'firebase/firestore';

export interface BuildFeedback {
  id?: string;
  buildId: string;
  userId: string;
  userEmail: string;
  userName: string;
  rating: number; // 1-5 stars
  comment: string;
  buildName: string;
  buildDescription: string;
  buildBudget: string;
  buildUseCase: string;
  isPublic: boolean; // Whether user allows this to be used as testimonial
  createdAt: Date;
  updatedAt: Date;
}

export class FeedbackService {
  private static readonly COLLECTION_NAME = 'build-feedbacks';
  /**
   * Submit feedback for a build
   */
  static async submitFeedback(feedback: Omit<BuildFeedback, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      const feedbackData: Omit<BuildFeedback, 'id'> = {
        ...feedback,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const docRef = await FirestoreService.addDocument(this.COLLECTION_NAME, feedbackData);
      return docRef.id;
    } catch (error) {
      console.error('Error submitting feedback:', error);
      throw error;
    }
  }

  /**
   * Get feedback for a specific build
   */
  static async getBuildFeedback(buildId: string): Promise<BuildFeedback[]> {
    try {
      const feedbacks: DocumentData[] = await FirestoreService.getDocuments(
        this.COLLECTION_NAME,
        [{ field: 'buildId', operator: '==', value: buildId }]
      );

      return feedbacks.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BuildFeedback));
    } catch (error) {
      console.error('Error getting build feedback:', error);
      throw error;
    }
  }

  /**
   * Get user's feedback history
   */
  static async getUserFeedback(userId: string): Promise<BuildFeedback[]> {
    try {
      const feedbacks: DocumentData[] = await FirestoreService.getDocuments(
        this.COLLECTION_NAME,
        [{ field: 'userId', operator: '==', value: userId }]
      );

      return feedbacks.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BuildFeedback));
    } catch (error) {
      console.error('Error getting user feedback:', error);
      throw error;
    }
  }

  /**
   * Get public testimonials (feedback marked as public with high ratings)
   */
  static async getTestimonials(limit: number = 10): Promise<BuildFeedback[]> {
    try {
      const feedbacks: DocumentData[] = await FirestoreService.getDocuments(
        this.COLLECTION_NAME,
        [
          { field: 'isPublic', operator: '==', value: true },
          { field: 'rating', operator: '>=', value: 4 }
        ],
        limit,
        [{ field: 'createdAt', direction: 'desc' }]
      );

      return feedbacks.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BuildFeedback));
    } catch (error) {
      console.error('Error getting testimonials:', error);
      throw error;
    }
  }

  /**
   * Update feedback
   */
  static async updateFeedback(feedbackId: string, updates: Partial<BuildFeedback>): Promise<void> {
    try {
      const updateData = {
        ...updates,
        updatedAt: new Date(),
      };

      await FirestoreService.updateDocument(this.COLLECTION_NAME, feedbackId, updateData);
    } catch (error) {
      console.error('Error updating feedback:', error);
      throw error;
    }
  }

  /**
   * Delete feedback
   */
  static async deleteFeedback(feedbackId: string): Promise<void> {
    try {
      await FirestoreService.deleteDocument(this.COLLECTION_NAME, feedbackId);
    } catch (error) {
      console.error('Error deleting feedback:', error);
      throw error;
    }
  }

  /**
   * Get average rating for a build
   */
  static async getBuildAverageRating(buildId: string): Promise<{ average: number; count: number }> {
    try {
      const feedbacks = await this.getBuildFeedback(buildId);

      if (feedbacks.length === 0) {
        return { average: 0, count: 0 };
      }

      const totalRating = feedbacks.reduce((sum, feedback) => sum + feedback.rating, 0);
      const average = totalRating / feedbacks.length;

      return { average: Math.round(average * 10) / 10, count: feedbacks.length };
    } catch (error) {
      console.error('Error calculating average rating:', error);
      return { average: 0, count: 0 };
    }
  }
}
