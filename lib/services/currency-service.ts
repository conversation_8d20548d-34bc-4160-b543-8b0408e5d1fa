'use client';

import { getFirestore, doc, getDoc } from "firebase/firestore";
import { app } from "./firebase-config";
import { CountryCode } from "@/types/affiliate-links";

// Initialize Firestore
const db = getFirestore(app);

/**
 * Currency mapping for countries
 */
export const COUNTRY_CURRENCY_MAP: Record<CountryCode, { currency: string; symbol: string }> = {
  // North America
  'US': { currency: 'USD', symbol: '$' },
  'CA': { currency: 'CAD', symbol: 'C$' },
  'MX': { currency: 'MXN', symbol: 'MX$' },

  // Europe
  'GB': { currency: 'GBP', symbol: '£' },
  'DE': { currency: 'EUR', symbol: '€' },
  'FR': { currency: 'EUR', symbol: '€' },
  'ES': { currency: 'EUR', symbol: '€' },
  'IT': { currency: 'EUR', symbol: '€' },
  'NL': { currency: 'EUR', symbol: '€' },
  'SE': { currency: 'SEK', symbol: 'kr' },
  'NO': { currency: 'NOK', symbol: 'kr' },
  'DK': { currency: 'DKK', symbol: 'kr' },
  'FI': { currency: 'EUR', symbol: '€' },
  'PL': { currency: 'PLN', symbol: 'zł' },

  // Asia
  'IN': { currency: 'INR', symbol: '₹' },
  'CN': { currency: 'CNY', symbol: '¥' },
  'JP': { currency: 'JPY', symbol: '¥' },
  'KR': { currency: 'KRW', symbol: '₩' },
  'SG': { currency: 'SGD', symbol: 'S$' },
  'MY': { currency: 'MYR', symbol: 'RM' },
  'ID': { currency: 'IDR', symbol: 'Rp' },
  'TH': { currency: 'THB', symbol: '฿' },
  'PH': { currency: 'PHP', symbol: '₱' },
  'AE': { currency: 'AED', symbol: 'د.إ' },
  'SA': { currency: 'SAR', symbol: '﷼' },

  // Oceania
  'AU': { currency: 'AUD', symbol: 'A$' },
  'NZ': { currency: 'NZD', symbol: 'NZ$' },

  // South America
  'BR': { currency: 'BRL', symbol: 'R$' },
  'AR': { currency: 'ARS', symbol: 'AR$' },
  'CL': { currency: 'CLP', symbol: 'CL$' },
  'CO': { currency: 'COP', symbol: 'CO$' },
  'PE': { currency: 'PEN', symbol: 'S/' },

  // Africa
  'ZA': { currency: 'ZAR', symbol: 'R' },
  'NG': { currency: 'NGN', symbol: '₦' },
  'EG': { currency: 'EGP', symbol: 'E£' },
  'KE': { currency: 'KES', symbol: 'KSh' },
};

/**
 * Default currency settings
 */
export const DEFAULT_CURRENCY = {
  country: 'US' as CountryCode,
  currency: 'USD',
  symbol: '$'
};

/**
 * Interface for currency rates data
 */
export interface CurrencyRates {
  base: string;
  date: string;
  timestamp: Date;
  rates: Record<string, number>;
  lastUpdated: Date;
  source: string;
}

/**
 * Currency Service
 * Provides methods for currency conversion and formatting
 */
export class CurrencyService {
  private static cachedRates: CurrencyRates | null = null;
  private static lastFetch: number = 0;
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get currency information for a country
   * @param countryCode Country code
   * @returns Currency information
   */
  static getCurrencyForCountry(countryCode?: string): { currency: string; symbol: string } {
    if (!countryCode || !(countryCode.toUpperCase() as CountryCode in COUNTRY_CURRENCY_MAP)) {
      return DEFAULT_CURRENCY;
    }

    return COUNTRY_CURRENCY_MAP[countryCode.toUpperCase() as CountryCode];
  }

  /**
   * Fetch latest currency rates from Firestore
   * @returns Promise with currency rates
   */
  static async getCurrencyRates(): Promise<CurrencyRates | null> {
    try {
      // Check cache first
      const now = Date.now();
      if (this.cachedRates && (now - this.lastFetch) < this.CACHE_DURATION) {
        return this.cachedRates;
      }

      const docRef = doc(db, "currency-rates", "latest");
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const data = docSnap.data() as CurrencyRates;

        // Update cache
        this.cachedRates = data;
        this.lastFetch = now;

        return data;
      } else {
        console.warn("No currency rates found in Firestore");
        return null;
      }
    } catch (error) {
      console.error("Error fetching currency rates:", error);
      return null;
    }
  }

  /**
   * Convert USD price to target currency
   * @param usdPrice Price in USD
   * @param targetCurrency Target currency code
   * @returns Converted price or original price if conversion fails
   */
  static async convertFromUSD(usdPrice: number, targetCurrency: string): Promise<number> {
    try {
      // If target currency is USD, return original price
      if (targetCurrency === 'USD') {
        return usdPrice;
      }

      const rates = await this.getCurrencyRates();
      if (!rates?.rates[targetCurrency]) {
        console.warn(`No exchange rate found for ${targetCurrency}, returning USD price`);
        return usdPrice;
      }

      const rate = rates.rates[targetCurrency];
      return usdPrice * rate;
    } catch (error) {
      console.error("Error converting currency:", error);
      return usdPrice;
    }
  }

  /**
   * Convert price from any currency to USD
   * @param price Price in source currency
   * @param sourceCurrency Source currency code
   * @returns Price in USD or original price if conversion fails
   */
  static async convertToUSD(price: number, sourceCurrency: string): Promise<number> {
    try {
      // If source currency is USD, return original price
      if (sourceCurrency === 'USD') {
        return price;
      }

      const rates = await this.getCurrencyRates();
      if (!rates?.rates[sourceCurrency]) {
        console.warn(`No exchange rate found for ${sourceCurrency}, returning original price`);
        return price;
      }

      const rate = rates.rates[sourceCurrency];
      return price / rate;
    } catch (error) {
      console.error("Error converting currency:", error);
      return price;
    }
  }

  /**
   * Format price with currency symbol
   * @param price Price to format
   * @param countryCode Country code for currency formatting
   * @param options Formatting options
   * @returns Formatted price string
   */
  static formatPrice(
    price: number,
    countryCode?: string,
    options: {
      showSymbol?: boolean;
      decimals?: number;
      locale?: string;
    } = {}
  ): string {
    const { showSymbol = true, decimals = 2, locale } = options;
    const currencyInfo = this.getCurrencyForCountry(countryCode);

    // Format the number with appropriate locale
    const formattedNumber = price.toLocaleString(locale || 'en-US', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });

    if (showSymbol) {
      return `${currencyInfo.symbol}${formattedNumber}`;
    }

    return formattedNumber;
  }

  /**
   * Convert and format price for display
   * @param usdPrice Price in USD
   * @param countryCode Target country code
   * @param options Formatting options
   * @returns Promise with formatted price string
   */
  static async convertAndFormatPrice(
    usdPrice: number,
    countryCode?: string,
    options: {
      showSymbol?: boolean;
      decimals?: number;
      locale?: string;
    } = {}
  ): Promise<string> {
    try {
      const currencyInfo = this.getCurrencyForCountry(countryCode);
      const convertedPrice = await this.convertFromUSD(usdPrice, currencyInfo.currency);

      if (convertedPrice === usdPrice) {
        return this.formatPrice(usdPrice, 'US', options);
      }

      return this.formatPrice(convertedPrice, countryCode, options);
    } catch (error) {
      console.error("Error converting and formatting price:", error);
      // Fallback to USD formatting
      return this.formatPrice(usdPrice, 'US', options);
    }
  }

  /**
   * Get exchange rate for a currency
   * @param currency Currency code
   * @returns Exchange rate or null if not found
   */
  static async getExchangeRate(currency: string): Promise<number | null> {
    try {
      if (currency === 'USD') {
        return 1;
      }

      const rates = await this.getCurrencyRates();
      if (!rates?.rates[currency]) {
        return null;
      }

      return rates.rates[currency];
    } catch (error) {
      console.error("Error getting exchange rate:", error);
      return null;
    }
  }

  /**
   * Clear cached rates (useful for testing or manual refresh)
   */
  static clearCache(): void {
    this.cachedRates = null;
    this.lastFetch = 0;
  }
}

export default CurrencyService;
