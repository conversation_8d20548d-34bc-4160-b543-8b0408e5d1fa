'use client';

import { FirestoreService } from './firestore-service';
import { ComponentAffiliateLinks, CountryCode, MarketplaceType } from '@/types/affiliate-links';
import { ComponentType } from '@/types/pc-build';
import { 
  generateSearchUrl, 
  getAvailableMarketplacesForCountry,
  getMarketplaceName 
} from '@/lib/utils/country-marketplace-data';

export interface PurchaseLink {
  marketplace: MarketplaceType;
  marketplaceName: string;
  url: string;
  isAffiliate: boolean;
  isActive?: boolean;
}

/**
 * Affiliate Link Service
 * Handles retrieval of affiliate links and generation of fallback search links
 */
export class AffiliateLinkService {
  /**
   * Get purchase links for a component based on user's country
   * @param componentType Component type
   * @param componentName Component name
   * @param componentBrand Component brand
   * @param componentModel Component model
   * @param userCountry User's country code
   * @returns Array of purchase links (affiliate + search fallbacks)
   */
  static async getPurchaseLinks(
    componentType: ComponentType,
    componentName: string,
    componentBrand: string,
    componentModel: string,
    userCountry: CountryCode
  ): Promise<PurchaseLink[]> {
    try {
      // Get affiliate links from Firestore
      const affiliateLinks = await this.getAffiliateLinks(
        componentType,
        componentName,
        componentBrand,
        componentModel,
        userCountry
      );

      // Get available marketplaces for the user's country
      const availableMarketplaces = getAvailableMarketplacesForCountry(userCountry);
      
      // Create a map of existing affiliate marketplaces
      const affiliateMarketplaces = new Set(affiliateLinks.map(link => link.marketplace));
      
      // Generate search links for marketplaces without affiliate links
      const searchLinks = availableMarketplaces
        .filter(marketplace => !affiliateMarketplaces.has(marketplace))
        .map(marketplace => ({
          marketplace,
          marketplaceName: getMarketplaceName(marketplace),
          url: generateSearchUrl(userCountry, marketplace, `${componentBrand} ${componentModel}`),
          isAffiliate: false,
          isActive: true
        }));

      // Combine affiliate links and search links
      const allLinks = [...affiliateLinks, ...searchLinks];
      
      // Sort by priority: affiliate links first, then by marketplace popularity
      const marketplacePriority: Record<string, number> = {
        amazon: 1,
        newegg: 2,
        bestbuy: 3,
        walmart: 4,
        microcenter: 5,
        flipkart: 6,
        scan: 7,
        overclockers: 8,
        mwave: 9,
        pccasegear: 10
      };

      return allLinks.sort((a, b) => {
        // Affiliate links first
        if (a.isAffiliate && !b.isAffiliate) return -1;
        if (!a.isAffiliate && b.isAffiliate) return 1;
        
        // Then by marketplace priority
        const aPriority = marketplacePriority[a.marketplace] || 999;
        const bPriority = marketplacePriority[b.marketplace] || 999;
        return aPriority - bPriority;
      });

    } catch (error) {
      console.error('Error getting purchase links:', error);
      
      // Fallback: return search links for major marketplaces
      const fallbackMarketplaces: MarketplaceType[] = ['amazon', 'newegg'];
      return fallbackMarketplaces.map(marketplace => ({
        marketplace,
        marketplaceName: getMarketplaceName(marketplace),
        url: generateSearchUrl(userCountry, marketplace, `${componentBrand} ${componentModel}`),
        isAffiliate: false,
        isActive: true
      }));
    }
  }

  /**
   * Get affiliate links for a specific component and country
   * @param componentType Component type
   * @param componentName Component name
   * @param componentBrand Component brand
   * @param componentModel Component model
   * @param userCountry User's country code
   * @returns Array of affiliate purchase links
   */
  private static async getAffiliateLinks(
    componentType: ComponentType,
    componentName: string,
    componentBrand: string,
    componentModel: string,
    userCountry: CountryCode
  ): Promise<PurchaseLink[]> {
    try {
      const collectionName = `affiliate-${componentType.toLowerCase().replace(' ', '-')}`;
      
      // Query for the specific component
      const components = await FirestoreService.queryDocuments(collectionName, [
        { fieldPath: 'name', opStr: '==', value: componentName },
        { fieldPath: 'brand', opStr: '==', value: componentBrand },
        { fieldPath: 'model', opStr: '==', value: componentModel }
      ]) as ComponentAffiliateLinks[];

      if (components.length === 0) {
        return [];
      }

      const component = components[0];
      
      // Find country-specific links
      const countryLinks = component.countryLinks.find(
        cl => cl.countryCode === userCountry
      );

      if (!countryLinks || countryLinks.links.length === 0) {
        return [];
      }

      // Convert to purchase links format
      return countryLinks.links
        .filter(link => link.isActive)
        .map(link => ({
          marketplace: link.marketplace,
          marketplaceName: getMarketplaceName(link.marketplace),
          url: link.url,
          isAffiliate: true,
          isActive: link.isActive
        }));

    } catch (error) {
      console.error('Error getting affiliate links:', error);
      return [];
    }
  }

  /**
   * Get all affiliate links for a component (for admin purposes)
   * @param componentType Component type
   * @param componentName Component name
   * @param componentBrand Component brand
   * @param componentModel Component model
   * @returns Component affiliate links data
   */
  static async getComponentAffiliateData(
    componentType: ComponentType,
    componentName: string,
    componentBrand: string,
    componentModel: string
  ): Promise<ComponentAffiliateLinks | null> {
    try {
      const collectionName = `affiliate-${componentType.toLowerCase().replace(' ', '-')}`;
      
      const components = await FirestoreService.queryDocuments(collectionName, [
        { fieldPath: 'name', opStr: '==', value: componentName },
        { fieldPath: 'brand', opStr: '==', value: componentBrand },
        { fieldPath: 'model', opStr: '==', value: componentModel }
      ]) as ComponentAffiliateLinks[];

      return components.length > 0 ? components[0] : null;
    } catch (error) {
      console.error('Error getting component affiliate data:', error);
      return null;
    }
  }

  /**
   * Check if a component has affiliate links for a specific country
   * @param componentType Component type
   * @param componentName Component name
   * @param componentBrand Component brand
   * @param componentModel Component model
   * @param userCountry User's country code
   * @returns Boolean indicating if affiliate links exist
   */
  static async hasAffiliateLinks(
    componentType: ComponentType,
    componentName: string,
    componentBrand: string,
    componentModel: string,
    userCountry: CountryCode
  ): Promise<boolean> {
    const affiliateLinks = await this.getAffiliateLinks(
      componentType,
      componentName,
      componentBrand,
      componentModel,
      userCountry
    );
    
    return affiliateLinks.length > 0;
  }
}

export default AffiliateLinkService;
