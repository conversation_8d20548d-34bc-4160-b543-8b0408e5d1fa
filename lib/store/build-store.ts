'use client';

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { v4 as uuidv4 } from 'uuid';
import { PCBuild, ChatMessage, RecommendedBuilds, PCComponent, ComponentType } from '@/types/pc-build';
import { VertexAIService } from '@/lib/services/vertex-ai-service';
import { FirestoreService } from '@/lib/services/firestore-service';
import { Chat } from '@google/genai';
import { ComponentAffiliateLinks } from '@/types/affiliate-links';

interface BuildState {
  // Current build being worked on
  currentBuild: PCBuild | null;
  setCurrentBuild: (build: PCBuild | null) => void;

  // Recommended builds
  recommendedBuilds: RecommendedBuilds;
  chatSession: Chat | null;
  setChatSession: (session: Chat | null) => void;
  isLoadingRecommendedBuilds: boolean;
  loadRecommendedBuilds: (forceRefresh?: boolean) => Promise<void>;

  // User's saved builds
  userBuilds: PCBuild[];
  isLoadingUserBuilds: boolean;
  loadUserBuilds: (userId: string) => Promise<void>;
  saveBuild: (userId: string, build: PCBuild) => Promise<PCBuild>;
  deleteBuild: (buildId: string) => Promise<boolean>;

  // Chat messages for the current build
  chatMessages: ChatMessage[];
  isLoadingChatResponse: boolean;
  addChatMessage: (message: ChatMessage) => void;
  updateChatMessage: (messageId: string, isNew: boolean) => void;
  sendChatMessage: (message: string) => Promise<string>;
  initializeChat: () => Promise<void>;
  clearChatMessages: () => void;

  // Component extraction for affiliate links
  extractAndSaveComponents: (build: PCBuild) => Promise<void>;

  // No longer needed - build generation is handled by the chat
}

// Default system message for the chat
const defaultSystemMessage: ChatMessage = {
  role: 'system',
  id: 'system',
  content: `You are DIYPC, an AI assistant specialized in helping users build custom PCs.

Your primary role is to help users create PC builds based on their requirements.

IMPORTANT: When a user asks for a PC build recommendation:
1. If the user hasn't specified their budget and use case, ask for this information first
2. Once you have the necessary information (use case and budget), provide helpful information about components suitable for their needs
3. Be ready to explain why specific components are recommended
4. When you're ready to generate a complete build, you MUST include a complete PC build specification in JSON format at the end of your message

IMPORTANT: Always provide ALL prices in US Dollars (USD) only. Do not use any other currency.
All component prices and total prices must be in USD regardless of the user's location or preferences.
This ensures price consistency across the application and proper currency conversion.
If user had provided budget in their local currency, convert it to USD before providing a build recommendation.
Ask for clarification if you are not sure what currency they are using.

The JSON format for the PC build MUST follow this structure:
\`\`\`json
{
  "name": "Build Name",
  "description": "Brief description of the build",
  "totalPrice": 1500,
  "useCase": "gaming", // Must be one of: "gaming", "content", "work", "everyday use"
  "budget": "$1500", // Format as a string with dollar sign
  "components": [
    {
      "type": "CPU", // Must be one of: "CPU", "GPU", "RAM", "Motherboard", "SSD", "HDD", "PSU", "Case", "CPU Cooler"
      "name": "Intel Core i7-13700K",
      "brand": "Intel",
      "model": "Core i7-13700K",
      "price": 399,
      "specs": [
        { "name": "Cores", "value": "16 (8P+8E)" },
        { "name": "Base Clock", "value": "3.4 GHz" }
      ]
    },
    // Include all necessary components (CPU, GPU, RAM, Motherboard, SSD/HDD, PSU, Case, CPU Cooler if needed)
  ],
  "compatibility": {
    "isCompatible": true,
    "issues": []
  }
}
\`\`\`

Make sure to include ALL required components in your build. The total price should be the sum of all component prices.

CRITICAL: All prices must be in US Dollars (USD). Do not use any other currency.

If you are not sure what to recommend, ask for more information.

You should be knowledgeable about:
- PC components (CPUs, GPUs, RAM, motherboards, storage, PSUs, cases, cooling)
- Performance characteristics for different use cases (gaming, content creation, work, everyday use)
- Price-to-performance considerations at different budget levels
- Component compatibility
- Current market trends and recommendations

Always be helpful, informative, and focused on the user's specific needs.`
};

export const useBuildStore = create<BuildState>()(
  persist(
    (set, get) => ({
      // Current build
      currentBuild: null,
      setCurrentBuild: (build) => set({ currentBuild: build }),

      // Recommended builds
      recommendedBuilds: {
        gaming: [],
        content: [],
        work: [],
      },
      chatSession: null,
      setChatSession: (session) => set({ chatSession: session }),
      isLoadingRecommendedBuilds: false,
      loadRecommendedBuilds: async (forceRefresh = false) => {
        // Check if we already have builds and don't need to refresh
        const { recommendedBuilds } = get();
        if (!forceRefresh &&
          recommendedBuilds.gaming.length > 0 &&
          recommendedBuilds.content.length > 0 &&
          recommendedBuilds.work.length > 0) {
          console.log('Using existing recommended builds from store');
          return;
        }

        set({ isLoadingRecommendedBuilds: true });

        try {
          // Generate builds using the Vertex AI API with caching
          const builds = await VertexAIService.generatePreConfiguredBuilds(forceRefresh);
          const gaming = builds.gaming.toSorted((a, b) => a.totalPrice - b.totalPrice);
          const content = builds.content.toSorted((a, b) => a.totalPrice - b.totalPrice);
          const work = builds.work.toSorted((a, b) => a.totalPrice - b.totalPrice);
          const sortedBuilds = { gaming, content, work };
          set({ recommendedBuilds: sortedBuilds });

          // Extract and save components from all recommended builds
          const { extractAndSaveComponents } = get();
          for (const build of [...gaming, ...content, ...work]) {
            await extractAndSaveComponents(build);
          }
        } catch (error) {
          console.error('Error loading recommended builds:', error);

          // If we have no builds at all, use default builds as fallback
          if (recommendedBuilds.gaming.length === 0 ||
            recommendedBuilds.content.length === 0 ||
            recommendedBuilds.work.length === 0) {
            try {
              const defaultBuilds = VertexAIService.getDefaultPreConfiguredBuilds();
              set({ recommendedBuilds: defaultBuilds });
            } catch (fallbackError) {
              console.error('Error loading default recommended builds:', fallbackError);
            }
          }
        } finally {
          set({ isLoadingRecommendedBuilds: false });
        }
      },

      // User's saved builds
      userBuilds: [],
      isLoadingUserBuilds: false,
      loadUserBuilds: async (userId) => {
        set({ isLoadingUserBuilds: true });

        try {
          const builds = await FirestoreService.getUserBuilds(userId);
          set({ userBuilds: builds as PCBuild[] });
        } catch (error) {
          console.error('Error loading user builds:', error);
        } finally {
          set({ isLoadingUserBuilds: false });
        }
      },
      saveBuild: async (userId, build) => {
        try {
          const savedBuild = await FirestoreService.saveBuild(userId, build);

          // Update user builds list
          const { userBuilds, extractAndSaveComponents } = get();
          set({ userBuilds: [...userBuilds, savedBuild as PCBuild] });

          // Extract and save components for affiliate links
          await extractAndSaveComponents(savedBuild as PCBuild);

          return savedBuild as PCBuild;
        } catch (error) {
          console.error('Error saving build:', error);
          throw error;
        }
      },
      deleteBuild: async (buildId) => {
        try {
          await FirestoreService.deleteBuild(buildId);

          // Update user builds list
          const { userBuilds } = get();
          set({ userBuilds: userBuilds.filter(build => build.id !== buildId) });

          return true;
        } catch (error) {
          console.error('Error deleting build:', error);
          throw error;
        }
      },

      // Chat messages - always start with just the system message
      chatMessages: [defaultSystemMessage],
      isLoadingChatResponse: false,
      addChatMessage: (message) => {
        const { chatMessages } = get();
        set({ chatMessages: [...chatMessages, message] });
      },
      updateChatMessage: (messageId, isNew) => {
        const { chatMessages } = get();
        set({
          chatMessages: chatMessages.map(msg => msg.id === messageId ? { ...msg, isNew } : msg)
        });
      },
      initializeChat: async () => {
        const { chatMessages, setChatSession } = get();

        // Get the system message
        const systemMessage = chatMessages.find(msg => msg.role === 'system');

        if (!systemMessage) {
          console.error('No system message found in chat messages');
          return;
        }

        try {
          console.log('Initializing chat session with system message');

          // Initialize the chat session with system content and initial user message
          const chatSession = VertexAIService.startChat(
            systemMessage.content
          );
          setChatSession(chatSession);

        } catch (error) {
          console.error('Error initializing chat:', error);
        }
      },

      sendChatMessage: async (message: string) => {
        const { addChatMessage, chatSession, setCurrentBuild } = get();

        // Add user message to chat
        const userMessage: ChatMessage = { role: 'user', content: message, timestamp: new Date(), isNew: true, id: uuidv4() };
        addChatMessage(userMessage);

        set({ isLoadingChatResponse: true });

        try {
          // Get AI response using the existing chat session
          const response = await chatSession?.sendMessage({
            message: message
          });

          // Ensure we have a valid response string
          const responseText = response?.text?.trim() ?? '';

          // Try to extract JSON PC build data from the response
          let pcBuildData: PCBuild | null = null;
          let displayResponse = responseText;

          // Look for JSON data in the response (between ```json and ```)
          const jsonRegex = /```json\s*({[\s\S]*?})\s*```/;
          const jsonMatch = jsonRegex.exec(responseText);
          if (jsonMatch?.[1]) {
            try {
              // Parse the JSON data
              const jsonStr = jsonMatch[1].replace(/\/\/.*$/gm, ''); // Remove comments
              pcBuildData = JSON.parse(jsonStr) as PCBuild;
              console.log('Extracted PC build data:', pcBuildData);

              // Set the current build in the store
              if (pcBuildData?.components?.length > 0 &&
                pcBuildData?.totalPrice &&
                pcBuildData?.useCase) {
                setCurrentBuild(pcBuildData);

                // Extract and save components for affiliate links
                const { extractAndSaveComponents } = get();
                extractAndSaveComponents(pcBuildData);
              }

              // Remove the JSON data from the displayed response for cleaner chat
              displayResponse = responseText.replace(/```json\s*{[\s\S]*?}\s*```/,
                '**PC Build Generated!** I\'ve created a custom build based on your requirements. You can now proceed to the component selection step to review the build.');
            } catch (jsonError) {
              console.error('Error parsing PC build JSON:', jsonError);
            }
          }

          // Add AI response to chat
          const assistantMessage: ChatMessage = { role: 'assistant', content: displayResponse, timestamp: new Date(), isNew: true, id: uuidv4() };
          addChatMessage(assistantMessage);

          // Return the response text
          return responseText;
        } catch (error) {
          console.error('Error in chat:', error);
          // Return empty string on error
          return '';
        } finally {
          set({ isLoadingChatResponse: false });
        }
      },
      clearChatMessages: () => {
        // Reset the chat messages in the store to just the system message
        set({
          chatMessages: [defaultSystemMessage]
        });

        // Reset the chat session in the VertexAIService
        try {
          // Create a new chat session with the system message content
          // We'll add a dummy initial user message that won't be displayed
          VertexAIService.startChat(
            defaultSystemMessage.content
          );
        } catch (error) {
          console.error('Error resetting chat session:', error);
        }
      },

      // Component extraction for affiliate links
      extractAndSaveComponents: async (build: PCBuild) => {
        if (!build?.components?.length) {
          console.error('No components to extract');
          return;
        }

        try {
          // Group components by type
          const componentsByType = build.components.reduce((acc, component) => {
            if (!acc[component.type]) {
              acc[component.type] = [];
            }
            acc[component.type].push(component);
            return acc;
          }, {} as Record<ComponentType, PCComponent[]>);

          // Process each component type
          for (const [type, components] of Object.entries(componentsByType)) {
            const collectionName = `affiliate-${type.toLowerCase().replace(' ', '-')}`;

            // Get existing components from the collection
            const existingComponents = await FirestoreService.queryDocuments(collectionName, []);

            // Process each component
            for (const component of components) {
              // Check if component already exists in the collection
              const exists = existingComponents.some(existing => {
                const existingComponent = existing as unknown as ComponentAffiliateLinks;
                return existingComponent.name === component.name &&
                  existingComponent.brand === component.brand &&
                  existingComponent.model === component.model;
              });

              // If component doesn't exist, add it to the collection
              if (!exists) {
                const newComponent: ComponentAffiliateLinks = {
                  componentType: component.type,
                  name: component.name,
                  brand: component.brand,
                  model: component.model,
                  countryLinks: [],
                  isProcessed: false,
                  createdAt: new Date(),
                  updatedAt: new Date()
                };

                await FirestoreService.addDocument(collectionName, newComponent);
                console.log(`Added component to ${collectionName}:`, component.name);
              }
            }
          }
        } catch (error) {
          console.error('Error extracting and saving components:', error);
        }
      },

      // Build generation is now handled directly by the chat
    }),
    {
      name: 'build-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        recommendedBuilds: state.recommendedBuilds,
      }),
      // This onRehydrateStorage callback runs when the store is rehydrated from localStorage
      onRehydrateStorage: () => (state) => {
        // Always reset chat messages to just the system message on page load
        if (state) {
          state.chatMessages = [defaultSystemMessage];

          // Reset the chat session in VertexAIService
          try {
            VertexAIService.startChat(
              defaultSystemMessage.content
            );
          } catch (error) {
            console.error('Error resetting chat session during rehydration:', error);
          }
        }
      }
    }
  )
);

export default useBuildStore;
