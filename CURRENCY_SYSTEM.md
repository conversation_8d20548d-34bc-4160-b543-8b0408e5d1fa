# Currency Conversion System

This document explains the currency conversion system implemented in the DIYPC application.

## Overview

The currency conversion system automatically converts USD prices to the user's local currency based on their selected country. The system ensures that:

1. **AI always returns prices in USD** for consistency
2. **Prices are automatically converted** to user's local currency for display
3. **Exchange rates are updated daily** via Firebase Functions
4. **Fallback to USD** if conversion fails

## Architecture

### 1. Firebase Function (`updateCurrencyRates`)
- **Schedule**: Runs daily at 12:00 AM UTC
- **Source**: https://api.fxratesapi.com/latest
- **Storage**: Firestore collection `currency-rates`
- **Manual Trigger**: Available at `/updateCurrencyRatesManual` for testing

### 2. Currency Service (`lib/services/currency-service.ts`)
- **Country-to-Currency Mapping**: Maps country codes to currencies and symbols
- **Exchange Rate Fetching**: Retrieves rates from Firestore with caching
- **Conversion Functions**: USD ↔ Local Currency conversion
- **Formatting**: Proper currency symbol and locale formatting

### 3. React Hooks (`hooks/use-currency.ts`)
- **useCurrency**: Main hook for currency operations
- **useCurrencyBatch**: Convert multiple prices at once
- **usePriceDisplay**: Real-time price display with conversion

### 4. Price Components (`components/ui/price.tsx`)
- **Price**: Base component with automatic conversion
- **PriceLarge**: For prominent displays (total prices)
- **PriceSmall**: For compact displays (component lists)
- **PriceInline**: For inline text usage
- **PriceRange**: For min-max price ranges
- **PriceComparison**: For sale/discount displays

## Usage

### Basic Price Display
```tsx
import { PriceLarge, PriceSmall } from '@/components/ui/price';

// Always provide prices in USD
<PriceLarge usdPrice={1500} />
<PriceSmall usdPrice={299} />
```

### Using the Currency Hook
```tsx
import { useCurrency } from '@/hooks/use-currency';

function MyComponent() {
  const { convertPrice, formatPrice, getCurrencyInfo } = useCurrency();
  
  const handleConvert = async () => {
    const converted = await convertPrice(1000); // Convert $1000 USD
    const formatted = formatPrice(converted);
    const currencyInfo = getCurrencyInfo(); // Get user's currency info
  };
}
```

### Direct Service Usage
```tsx
import { CurrencyService } from '@/lib/services/currency-service';

// Convert USD to specific currency
const converted = await CurrencyService.convertFromUSD(1000, 'EUR');

// Format with currency symbol
const formatted = CurrencyService.formatPrice(converted, 'DE');

// Get exchange rate
const rate = await CurrencyService.getExchangeRate('EUR');
```

## Supported Countries and Currencies

The system supports 25+ countries across 6 regions:

- **North America**: US (USD), Canada (CAD), Mexico (MXN)
- **Europe**: UK (GBP), Germany/France/Spain/Italy/Netherlands/Finland (EUR), Sweden (SEK), Norway (NOK), Denmark (DKK), Poland (PLN)
- **Asia**: India (INR), China (CNY), Japan (JPY), South Korea (KRW), Singapore (SGD), Malaysia (MYR), Indonesia (IDR), Thailand (THB), Philippines (PHP), UAE (AED), Saudi Arabia (SAR)
- **Oceania**: Australia (AUD), New Zealand (NZD)
- **South America**: Brazil (BRL), Argentina (ARS), Chile (CLP), Colombia (COP), Peru (PEN)
- **Africa**: South Africa (ZAR), Nigeria (NGN), Egypt (EGP), Kenya (KES)

## AI Integration

### System Prompts Updated
All Vertex AI system prompts now include:
```
IMPORTANT: Always provide ALL prices in US Dollars (USD) only. Do not use any other currency.
All component prices and total prices must be in USD regardless of the user's location or preferences.
This ensures price consistency across the application and proper currency conversion.
```

### User Prompts Updated
Build generation prompts emphasize:
```
CRITICAL: All prices must be in US Dollars (USD). Do not use any other currency.
```

## Implementation Details

### Exchange Rate Caching
- **Client-side cache**: 5 minutes
- **Firestore storage**: Updated daily
- **Fallback**: USD if rates unavailable

### Error Handling
- **Network failures**: Fallback to USD
- **Invalid rates**: Fallback to USD
- **Missing currencies**: Fallback to USD
- **User feedback**: Error states in UI

### Performance
- **Lazy loading**: Rates fetched only when needed
- **Batch conversion**: Multiple prices converted efficiently
- **Memoization**: Prevents duplicate API calls

## Testing

Visit `/test-currency` to test the currency conversion system:
- Run conversion tests
- View price component examples
- Test with sample PC builds
- Clear cache for testing

## Deployment

### Firebase Functions
```bash
cd functions
npm run build
firebase deploy --only functions
```

### Manual Rate Update
Call the manual trigger function for immediate rate updates during development.

## Future Enhancements

1. **More Currencies**: Add support for additional countries
2. **Historical Rates**: Store rate history for analytics
3. **Rate Alerts**: Notify on significant rate changes
4. **Offline Support**: Cache rates for offline usage
5. **Custom Rates**: Allow manual rate overrides for testing
