'use client';

import React from 'react';
import { usePriceDisplay } from '@/hooks/use-currency';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface PriceProps {
  /**
   * Price in USD (always provide prices in USD for consistency)
   */
  usdPrice: number;

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Show loading skeleton while converting
   */
  showSkeleton?: boolean;

  /**
   * Custom formatting options
   */
  formatOptions?: {
    showSymbol?: boolean;
    decimals?: number;
    locale?: string;
  };

  /**
   * Fallback content while loading
   */
  fallback?: React.ReactNode;
}

/**
 * Price component that automatically converts USD to user's local currency
 * Always provide prices in USD to ensure consistency across the application
 */
export const Price = ({
  usdPrice,
  className,
  showSkeleton = true,
  // formatOptions,
  fallback
}: PriceProps) => {
  const { displayPrice, isLoading } = usePriceDisplay(usdPrice);

  if (isLoading) {
    if (showSkeleton) {
      return <Skeleton className={cn("h-4 w-16", className)} />;
    }

    if (fallback) {
      return <>{fallback}</>;
    }

    // Fallback to USD display
    return <span className={className}>${usdPrice.toFixed(2)}</span>;
  }

  return (
    <span className={className}>
      {displayPrice}
    </span>
  );
}

/**
 * Large price component for prominent display (e.g., total prices)
 */
export function PriceLarge({ usdPrice, className, ...props }: Omit<PriceProps, 'className'> & { className?: string }) {
  return (
    <Price
      usdPrice={usdPrice}
      className={cn("text-2xl font-bold", className)}
      {...props}
    />
  );
}

/**
 * Small price component for compact display (e.g., component lists)
 */
export function PriceSmall({ usdPrice, className, ...props }: Omit<PriceProps, 'className'> & { className?: string }) {
  return (
    <Price
      usdPrice={usdPrice}
      className={cn("text-sm font-medium", className)}
      {...props}
    />
  );
}

/**
 * Inline price component that doesn't break text flow
 */
export function PriceInline({ usdPrice, className, ...props }: Omit<PriceProps, 'className'> & { className?: string }) {
  return (
    <Price
      usdPrice={usdPrice}
      className={cn("font-medium", className)}
      showSkeleton={false}
      fallback={<span className={className}>${usdPrice.toFixed(2)}</span>}
      {...props}
    />
  );
}

/**
 * Price range component for displaying min-max prices
 */
interface PriceRangeProps {
  minUsdPrice: number;
  maxUsdPrice: number;
  className?: string;
  separator?: string;
}

export const PriceRange = ({
  minUsdPrice,
  maxUsdPrice,
  className,
  separator = " - "
}: PriceRangeProps) => {
  return (
    <span className={className}>
      <PriceInline usdPrice={minUsdPrice} />
      {separator}
      <PriceInline usdPrice={maxUsdPrice} />
    </span>
  );
}

/**
 * Price comparison component showing original and discounted prices
 */
interface PriceComparisonProps {
  originalUsdPrice: number;
  discountedUsdPrice: number;
  className?: string;
  showDiscount?: boolean;
}

export const PriceComparison = ({
  originalUsdPrice,
  discountedUsdPrice,
  className,
  showDiscount = true
}: PriceComparisonProps) => {
  const discountPercentage = Math.round(((originalUsdPrice - discountedUsdPrice) / originalUsdPrice) * 100);

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <PriceInline usdPrice={discountedUsdPrice} className="text-green-600 font-bold" />
      <PriceInline usdPrice={originalUsdPrice} className="text-muted-foreground line-through text-sm" />
      {showDiscount && discountPercentage > 0 && (
        <span className="text-green-600 text-sm font-medium">
          -{discountPercentage}%
        </span>
      )}
    </div>
  );
}

export default Price;
