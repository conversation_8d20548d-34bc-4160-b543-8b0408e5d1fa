'use client';

import { useRef, useEffect, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Star } from 'lucide-react';
import { FeedbackService, BuildFeedback } from '@/lib/services/feedback-service';

export function TestimonialsSection() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.2 });
  const [testimonials, setTestimonials] = useState<BuildFeedback[]>([]);

  // Fallback testimonials if no real feedback is available
  const fallbackTestimonials = [
    {
      userName: '<PERSON>',
      buildUseCase: 'Gaming Enthusiast',
      comment: 'DIYPC made building my first gaming PC so much easier than I expected. The AI recommendations were spot-on for my budget and the games I play.',
      rating: 5,
    },
    {
      userName: '<PERSON>',
      buildUseCase: 'Video Editor',
      comment: 'As a professional video editor, I needed a workstation that could handle 4K footage without breaking a sweat. DIYPC helped me build exactly what I needed.',
      rating: 5,
    },
    {
      userName: '<PERSON>',
      buildUseCase: 'Software Developer',
      comment: 'The compatibility checking feature saved me from making a costly mistake. The AI caught an issue I would have missed and suggested a better alternative.',
      rating: 5,
    },
  ];

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const realTestimonials = await FeedbackService.getTestimonials(6);
        if (realTestimonials.length > 0) {
          setTestimonials(realTestimonials);
        } else {
          // Use fallback testimonials if no real ones exist
          setTestimonials(fallbackTestimonials as BuildFeedback[]);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        // Use fallback testimonials on error
        setTestimonials(fallbackTestimonials as BuildFeedback[]);
      } finally {
        // Loading complete
      }
    };

    fetchTestimonials();
  }, []);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <section className="py-20 bg-background" ref={ref}>
      <div className="container max-w-7xl px-4 mx-auto">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Users Say</h2>
          <p className="text-muted-foreground text-lg">
            Join thousands of satisfied PC builders who have used DIYPC to create their perfect custom computers.
          </p>
        </motion.div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, i) => (
            <motion.div
              key={testimonial.id || `testimonial-${i}`}
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
              transition={{ duration: 0.4, delay: 0.1 * i }}
            >
              <Card className="h-full flex flex-col">
                <CardContent className="pt-6 flex-grow">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating || 5)].map((_, starIndex) => (
                      <Star key={`star-${starIndex}`} className="h-5 w-5 fill-primary text-primary" />
                    ))}
                    {[...Array(5 - (testimonial.rating || 5))].map((_, starIndex) => (
                      <Star key={`empty-star-${starIndex}`} className="h-5 w-5 text-muted-foreground" />
                    ))}
                  </div>
                  <p className="text-foreground">{testimonial.comment}</p>
                </CardContent>
                <CardFooter className="border-t pt-4">
                  <div className="flex items-center gap-3">
                    <Avatar>
                      <AvatarFallback>{getInitials(testimonial.userName)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium">{testimonial.userName}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.buildUseCase}</p>
                    </div>
                  </div>
                </CardFooter>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
