'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { PCBuild } from '@/types/pc-build';
import { motion } from 'framer-motion';
import { Check, Download, Save, Share2, MessageSquare, X } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { PriceLarge, PriceSmall } from '@/components/ui/price';
import { PurchaseLinks } from '@/components/build/purchase-links';
import { FeedbackDialog } from '@/components/build/feedback-dialog';
import { useAuthStore } from '@/lib/store/auth-store';
import { UserService } from '@/lib/services/user-service';
import { useState, useEffect } from 'react';
import { CountryCode } from '@/types/affiliate-links';
interface BuildSummaryProps {
  build: PCBuild;
  onSave?: () => void;
  onShare?: () => void;
  onExport?: () => void;
}

export const BuildSummary = ({ build, onSave, onShare, onExport }: BuildSummaryProps) => {
  const { user } = useAuthStore();
  const [userCountry, setUserCountry] = useState<CountryCode>('US'); // Default to US
  const [feedbackDialogOpen, setFeedbackDialogOpen] = useState(false);

  useEffect(() => {
    const getUserCountry = async () => {
      if (user) {
        try {
          const profile = await UserService.getProfileByUserId(user.uid);
          if (profile?.country) {
            setUserCountry(profile.country.toUpperCase() as CountryCode);
          }
        } catch (error) {
          console.error('Error getting user profile:', error);
        }
      }
    };

    getUserCountry();
  }, [user]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Card>
        <CardHeader>
          <CardTitle>Build Summary</CardTitle>
          <CardDescription>
            {build.description ?? `${build.useCase.charAt(0).toUpperCase() + build.useCase.slice(1)} PC build with a budget of ${build.budget}`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm text-muted-foreground">Total Price</p>
              <PriceLarge usdPrice={build.totalPrice} />
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Compatibility</p>
              {build.compatibility.isCompatible ? (
                <p className="text-green-500 flex items-center">
                  <Check className="mr-1 h-4 w-4" /> Compatible
                </p>
              ) : (
                <p className="text-red-500 flex items-center">
                  <X className="mr-1 h-4 w-4" /> Issues Detected
                </p>
              )}
            </div>
          </div>

          <Separator />

          <div className="space-y-2">
            <h3 className="font-medium">Components</h3>
            <ul className="space-y-2">
              {build.components.map((component) => (
                <li key={component.type} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">{component.type}</p>
                    <p className="text-sm text-muted-foreground">{component.name}</p>
                  </div>
                  <PriceSmall usdPrice={component.price} />
                </li>
              ))}
            </ul>
          </div>

          <Separator />

          <div className="space-y-2">
            <h3 className="font-medium">Buy Components</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {build.components.map((component) => (
                <Card key={component.type} className="overflow-hidden">
                  <CardHeader className="p-3 pb-0">
                    <CardTitle className="text-sm">{component.type}</CardTitle>
                    <CardDescription className="text-xs truncate">{component.name}</CardDescription>
                  </CardHeader>
                  <CardContent className="p-3 pt-2">
                    <PurchaseLinks
                      componentType={component.type}
                      componentName={component.name}
                      componentBrand={component.brand}
                      componentModel={component.model}
                      userCountry={userCountry}
                      maxLinks={4}
                      showAffiliateIndicator={true}
                      className="text-xs"
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            className="flex-1"
            onClick={onSave}
          >
            <Save className="mr-2 h-4 w-4" />
            Save Build
          </Button>
          <Button
            variant="outline"
            className="flex-1"
            onClick={onShare}
          >
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button
            variant="outline"
            className="flex-1"
            onClick={onExport}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button
            className="w-full mt-2"
            onClick={() => setFeedbackDialogOpen(true)}
            variant="default"
          >
            <MessageSquare className="mr-2 h-4 w-4" />
            Share Feedback
          </Button>
        </CardFooter>
      </Card>

      <FeedbackDialog
        open={feedbackDialogOpen}
        onOpenChange={setFeedbackDialogOpen}
        build={build}
      />
    </motion.div>
  );
}
