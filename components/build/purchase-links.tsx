'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ExternalLink, ShoppingCart } from 'lucide-react';
import { AffiliateLinkService, PurchaseLink } from '@/lib/services/affiliate-link-service';
import { ComponentType } from '@/types/pc-build';
import { CountryCode } from '@/types/affiliate-links';
import { cn } from '@/lib/utils';

interface PurchaseLinksProps {
  componentType: ComponentType;
  componentName: string;
  componentBrand: string;
  componentModel: string;
  userCountry: CountryCode;
  className?: string;
  maxLinks?: number;
  showAffiliateIndicator?: boolean;
}

export const PurchaseLinks = ({
  componentType,
  componentName,
  componentBrand,
  componentModel,
  userCountry,
  className,
  maxLinks = 4,
  showAffiliateIndicator = true
}: PurchaseLinksProps) => {
  const [purchaseLinks, setPurchaseLinks] = useState<PurchaseLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPurchaseLinks = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const links = await AffiliateLinkService.getPurchaseLinks(
          componentType,
          componentName,
          componentBrand,
          componentModel,
          userCountry
        );

        // Limit the number of links displayed
        setPurchaseLinks(links.slice(0, maxLinks));
      } catch (err) {
        console.error('Error fetching purchase links:', err);
        setError('Failed to load purchase links');
      } finally {
        setIsLoading(false);
      }
    };

    fetchPurchaseLinks();
  }, [componentType, componentName, componentBrand, componentModel, userCountry, maxLinks]);

  if (isLoading) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="text-sm font-medium text-muted-foreground">Purchase Links</div>
        <div className="flex flex-wrap gap-2">
          {Array.from({ length: 3 }).map((_, index) => (
            <Skeleton key={index} className="h-8 w-20" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="text-sm font-medium text-muted-foreground">Purchase Links</div>
        <div className="text-sm text-destructive">{error}</div>
      </div>
    );
  }

  if (purchaseLinks.length === 0) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="text-sm font-medium text-muted-foreground">Purchase Links</div>
        <div className="text-sm text-muted-foreground">No purchase links available</div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="text-sm font-medium text-muted-foreground">Purchase Links</div>
      <div className="flex flex-wrap gap-2">
        {purchaseLinks.map((link) => (
          <Button
            key={link.marketplace}
            variant="outline"
            size="sm"
            asChild
            className="h-8 px-3 text-xs"
          >
            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-1"
            >
              <ShoppingCart className="h-3 w-3" />
              {link.marketplaceName}
              <ExternalLink className="h-3 w-3" />
              {showAffiliateIndicator && link.isAffiliate && (
                <Badge variant="secondary" className="ml-1 h-4 px-1 text-[10px]">
                  Affiliate
                </Badge>
              )}
            </a>
          </Button>
        ))}
      </div>
    </div>
  );
}

interface PurchaseLinksCompactProps {
  componentType: ComponentType;
  componentName: string;
  componentBrand: string;
  componentModel: string;
  userCountry: CountryCode;
  className?: string;
}

export const PurchaseLinksCompact = ({
  componentType,
  componentName,
  componentBrand,
  componentModel,
  userCountry,
  className
}: PurchaseLinksCompactProps) => {
  const [purchaseLinks, setPurchaseLinks] = useState<PurchaseLink[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPurchaseLinks = async () => {
      try {
        setIsLoading(true);

        const links = await AffiliateLinkService.getPurchaseLinks(
          componentType,
          componentName,
          componentBrand,
          componentModel,
          userCountry
        );

        // Show only the top 2 links for compact view
        setPurchaseLinks(links.slice(0, 2));
      } catch (err) {
        console.error('Error fetching purchase links:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPurchaseLinks();
  }, [componentType, componentName, componentBrand, componentModel, userCountry]);

  if (isLoading) {
    return (
      <div className={cn("flex gap-1", className)}>
        <Skeleton className="h-6 w-16" />
        <Skeleton className="h-6 w-16" />
      </div>
    );
  }

  if (purchaseLinks.length === 0) {
    return null;
  }

  return (
    <div className={cn("flex gap-1", className)}>
      {purchaseLinks.map((link) => (
        <Button
          key={link.marketplace}
          variant="ghost"
          size="sm"
          asChild
          className="h-6 px-2 text-xs"
        >
          <a
            href={link.url}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-1"
          >
            {link.marketplaceName}
            <ExternalLink className="h-2.5 w-2.5" />
          </a>
        </Button>
      ))}
    </div>
  );
}

export default PurchaseLinks;
