'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { PCBuild, PCComponent } from '@/types/pc-build';
import { motion } from 'framer-motion';
import { Check, ChevronDown, ChevronUp, Edit, Info, X } from 'lucide-react';
import { Fragment, useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';
import { PriceLarge, PriceSmall } from '@/components/ui/price';

interface ComponentSelectorProps {
  build: PCBuild;
  onComponentChange?: (componentType: PCComponent['type'], component: PCComponent) => void;
  onViewExplanation?: (componentType: PCComponent['type']) => void;
  readOnly?: boolean;
}

export const ComponentSelector = ({
  build,
  onComponentChange,
  onViewExplanation,
  readOnly = false
}: ComponentSelectorProps) => {
  const [expandedComponent, setExpandedComponent] = useState<PCComponent['type'] | null>(null);

  const toggleExpand = (componentType: PCComponent['type']) => {
    if (expandedComponent === componentType) {
      setExpandedComponent(null);
    } else {
      setExpandedComponent(componentType);
    }
  };

  const componentOrder: PCComponent['type'][] = [
    'CPU',
    'CPU Cooler',
    'Motherboard',
    'RAM',
    'GPU',
    'SSD',
    'HDD',
    'PSU',
    'Case'
  ];

  const getComponentByType = (type: PCComponent['type']): PCComponent | undefined => {
    return build.components.find(component => component.type === type);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Components</h3>
          <p className="text-sm text-muted-foreground">
            {build.compatibility.isCompatible ? (
              <span className="flex items-center text-green-500">
                <Check className="mr-1 h-4 w-4" /> All components are compatible
              </span>
            ) : (
              <span className="flex items-center text-red-500">
                <X className="mr-1 h-4 w-4" /> Compatibility issues detected
              </span>
            )}
          </p>
        </div>
        <div className="text-right">
          <p className="text-sm text-muted-foreground">Total Price</p>
          <PriceLarge usdPrice={build.totalPrice} />
        </div>
      </div>

      <div className="space-y-2">
        {componentOrder.map((componentType) => {
          const component = getComponentByType(componentType);
          if (!component) return null;

          const isExpanded = expandedComponent === componentType;

          return (
            <motion.div
              key={componentType}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              layout
            >
              <Card className={isExpanded ? 'border-primary' : ''}>
                <CardHeader className="p-4 pb-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CardTitle className="text-base">{componentType}</CardTitle>
                      {!build.compatibility.isCompatible &&
                        build.compatibility.issues?.some(issue => issue.includes(componentType)) && (
                          <HoverCard>
                            <HoverCardTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-6 w-6 text-red-500">
                                <X className="h-4 w-4" />
                              </Button>
                            </HoverCardTrigger>
                            <HoverCardContent className="w-80">
                              <p className="text-sm">
                                {build.compatibility.issues?.find(issue =>
                                  issue.includes(componentType)
                                )}
                              </p>
                            </HoverCardContent>
                          </HoverCard>
                        )}
                    </div>
                    <div className="flex items-center gap-2">
                      <PriceSmall usdPrice={component.price} />
                      {!readOnly && onViewExplanation && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => onViewExplanation(componentType)}
                        >
                          <Info className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8"
                        onClick={() => toggleExpand(componentType)}
                      >
                        {isExpanded ? (
                          <ChevronUp className="h-4 w-4" />
                        ) : (
                          <ChevronDown className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                  <CardDescription className="mt-1">{component.name}</CardDescription>
                </CardHeader>

                {isExpanded && (
                  <>
                    <Separator />
                    <CardContent className="p-4 pt-4">
                      <div className="space-y-2">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="text-muted-foreground">Brand</div>
                          <div>{component.brand}</div>
                          <div className="text-muted-foreground">Model</div>
                          <div>{component.model}</div>

                          {component.specs.map((spec) => (
                            <Fragment key={spec.name}>
                              <div className="text-muted-foreground">{spec.name}</div>
                              <div>{spec.value}</div>
                            </Fragment>
                          ))}
                        </div>
                      </div>
                    </CardContent>

                    {!readOnly && onComponentChange && (
                      <CardFooter className="p-4 pt-0">
                        <Button
                          variant="outline"
                          size="sm"
                          className="w-full"
                          onClick={() => {
                            // This would open a component selection dialog in a real implementation
                            alert('Component selection would open here');
                          }}
                        >
                          <Edit className="mr-2 h-4 w-4" />
                          Change Component
                        </Button>
                      </CardFooter>
                    )}
                  </>
                )}
              </Card>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}
