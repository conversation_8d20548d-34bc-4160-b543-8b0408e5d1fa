/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import { onRequest } from "firebase-functions/v2/https";
import { onSchedule } from "firebase-functions/v2/scheduler";
import * as logger from "firebase-functions/logger";
import { initializeApp } from "firebase-admin/app";
import { getFirestore } from "firebase-admin/firestore";

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// Currency exchange rates function
export const updateCurrencyRates = onSchedule(
    {
        schedule: "0 0 * * *", // Run daily at 12:00 AM UTC
        timeZone: "UTC",
        memory: "256MiB",
        timeoutSeconds: 60,
    },
    async (event) => {
        try {
            logger.info("Starting currency rates update...");

            // Fetch exchange rates from FX Rates API
            const response = await fetch("https://api.fxratesapi.com/latest");

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(`API error: ${data.error?.info ?? 'Unknown error'}`);
            }

            // Prepare the currency rates document
            const currencyRatesDoc = {
                base: data.base, // Should be USD
                date: data.date,
                timestamp: new Date(),
                rates: data.rates,
                lastUpdated: new Date(),
                source: "fxratesapi.com"
            };

            // Store in Firestore
            await db.collection("currency-rates").doc("latest").set(currencyRatesDoc);

            logger.info(`Currency rates updated successfully. Base: ${data.base}, Date: ${data.date}`);
            logger.info(`Updated ${Object.keys(data.rates).length} exchange rates`);

        } catch (error) {
            logger.error("Error updating currency rates:", error);
            throw error;
        }
    }
);

// Manual trigger for testing currency rates update
export const updateCurrencyRatesManual = onRequest(
    {
        memory: "256MiB",
        timeoutSeconds: 60,
    },
    async (req, res) => {
        try {
            logger.info("Manual currency rates update triggered...");

            // Fetch exchange rates from FX Rates API
            const response = await fetch("https://api.fxratesapi.com/latest");

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (!data.success) {
                throw new Error(`API error: ${data.error?.info ?? 'Unknown error'}`);
            }

            // Prepare the currency rates document
            const currencyRatesDoc = {
                base: data.base, // Should be USD
                date: data.date,
                timestamp: new Date(),
                rates: data.rates,
                lastUpdated: new Date(),
                source: "fxratesapi.com"
            };

            // Store in Firestore
            await db.collection("currency-rates").doc("latest").set(currencyRatesDoc);

            logger.info(`Currency rates updated successfully. Base: ${data.base}, Date: ${data.date}`);

            res.status(200).json({
                success: true,
                message: "Currency rates updated successfully",
                base: data.base,
                date: data.date,
                ratesCount: Object.keys(data.rates).length
            });

        } catch (error) {
            logger.error("Error updating currency rates:", error);
            res.status(500).json({
                success: false,
                error: error instanceof Error ? error.message : "Unknown error"
            });
        }
    }
);

// Start writing functions
// https://firebase.google.com/docs/functions/typescript


