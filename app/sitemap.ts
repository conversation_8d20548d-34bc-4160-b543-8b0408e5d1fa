import { MetadataRoute } from "next";

export default function sitemap(): MetadataRoute.Sitemap {
    return [
        {
            url: "https://diypc.vercel.app",
            lastModified: new Date(),
            changeFrequency: "daily",
            priority: 1,
        },
        {
            url: "https://diypc.vercel.app/about",
            lastModified: new Date(),
            changeFrequency: "never",
            priority: 0.8,
        },
        {
            url: "https://diypc.vercel.app/contact",
            lastModified: new Date(),
            changeFrequency: "never",
            priority: 0.5,
        },
        {
            url: "https://diypc.vercel.app/privacy",
            lastModified: new Date(),
            changeFrequency: "never",
            priority: 0.5,
        },
        {
            url: "https://diypc.vercel.app/terms",
            lastModified: new Date(),
            changeFrequency: "never",
            priority: 0.5,
        },
        {
            url: "https://diypc.vercel.app/build",
            lastModified: new Date(),
            changeFrequency: "daily",
            priority: 0.8,
        },
        {
            url: "https://diypc.vercel.app/build/recommendations",
            lastModified: new Date(),
            changeFrequency: "daily",
            priority: 0.8,
        },
        {
            url: "https://diypc.vercel.app/build/quick",
            lastModified: new Date(),
            changeFrequency: "always",
            priority: 0.8,
        },
        {
            url: "https://diypc.vercel.app/build/new",
            lastModified: new Date(),
            changeFrequency: "always",
            priority: 0.8,
        },
        {
            url: "https://diypc.vercel.app/dashboard",
            lastModified: new Date(),
            changeFrequency: "daily",
            priority: 0.5,
        },
        {
            url: "https://diypc.vercel.app/auth/signin",
            lastModified: new Date(),
            changeFrequency: "never",
            priority: 0.5,
        },
        {
            url: "https://diypc.vercel.app/auth/signup",
            lastModified: new Date(),
            changeFrequency: "never",
            priority: 0.5,
        },
    ];
}
