'use client';

import { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PriceLarge, PriceSmall, PriceInline, PriceRange, PriceComparison } from '@/components/ui/price';
import { useCurrency } from '@/hooks/use-currency';
import { CurrencyService } from '@/lib/services/currency-service';
import { Loader2, RefreshCw } from 'lucide-react';

export default function TestCurrencyPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { getCurrencyInfo, userCountry } = useCurrency();

  const runTests = async () => {
    setIsLoading(true);
    setTestResults([]);
    const results: string[] = [];

    try {
      // Test 1: Get currency info
      const currencyInfo = getCurrencyInfo();
      results.push(`✅ Currency Info: ${currencyInfo.currency} (${currencyInfo.symbol}) for country ${userCountry}`);

      // Test 2: Fetch exchange rates
      const rates = await CurrencyService.getCurrencyRates();
      if (rates) {
        results.push(`✅ Exchange Rates: Fetched ${Object.keys(rates.rates).length} rates from ${rates.source}`);
        results.push(`📅 Last Updated: ${rates.lastUpdated.toLocaleString()}`);
      } else {
        results.push(`❌ Exchange Rates: Failed to fetch rates`);
      }

      // Test 3: Convert USD to user currency
      const usdAmount = 1000;
      const convertedAmount = await CurrencyService.convertFromUSD(usdAmount, currencyInfo.currency);
      results.push(`💱 Conversion: $${usdAmount} USD = ${convertedAmount.toFixed(2)} ${currencyInfo.currency}`);

      // Test 4: Format price
      const formattedPrice = CurrencyService.formatPrice(convertedAmount, userCountry);
      results.push(`💰 Formatted: ${formattedPrice}`);

      // Test 5: Get exchange rate
      const exchangeRate = await CurrencyService.getExchangeRate(currencyInfo.currency);
      if (exchangeRate) {
        results.push(`📊 Exchange Rate: 1 USD = ${exchangeRate.toFixed(4)} ${currencyInfo.currency}`);
      } else {
        results.push(`❌ Exchange Rate: Failed to get rate for ${currencyInfo.currency}`);
      }

    } catch (error) {
      results.push(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    setTestResults(results);
    setIsLoading(false);
  };

  const clearCache = () => {
    CurrencyService.clearCache();
    setTestResults([]);
  };

  return (
    <MainLayout>
      <div className="container max-w-4xl px-4 py-12 mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold tracking-tight">Currency Conversion Test</h1>
          <p className="text-muted-foreground mt-1">Test the currency conversion system</p>
        </div>

        <div className="grid gap-6">
          {/* Test Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Test Controls</CardTitle>
              <CardDescription>Run tests to verify currency conversion functionality</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={runTests} disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Run Tests
                </Button>
                <Button variant="outline" onClick={clearCache}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Clear Cache
                </Button>
              </div>

              {testResults.length > 0 && (
                <div className="space-y-2">
                  <h3 className="font-medium">Test Results:</h3>
                  <div className="bg-muted p-4 rounded-md">
                    {testResults.map((result, index) => (
                      <div key={index} className="font-mono text-sm">
                        {result}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Price Component Examples */}
          <Card>
            <CardHeader>
              <CardTitle>Price Component Examples</CardTitle>
              <CardDescription>Examples of different price components with currency conversion</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-medium mb-2">Large Price (Total Build Price)</h3>
                <PriceLarge usdPrice={1500} />
              </div>

              <div>
                <h3 className="font-medium mb-2">Small Price (Component Price)</h3>
                <PriceSmall usdPrice={299} />
              </div>

              <div>
                <h3 className="font-medium mb-2">Inline Price (In Text)</h3>
                <p>This gaming PC costs <PriceInline usdPrice={1200} /> and includes a powerful GPU.</p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Price Range</h3>
                <PriceRange minUsdPrice={800} maxUsdPrice={1500} />
              </div>

              <div>
                <h3 className="font-medium mb-2">Price Comparison (Sale)</h3>
                <PriceComparison originalUsdPrice={399} discountedUsdPrice={299} />
              </div>
            </CardContent>
          </Card>

          {/* Sample PC Build */}
          <Card>
            <CardHeader>
              <CardTitle>Sample PC Build</CardTitle>
              <CardDescription>Example build with currency conversion</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="font-medium">Gaming PC Build</h3>
                    <p className="text-sm text-muted-foreground">High-performance gaming setup</p>
                  </div>
                  <PriceLarge usdPrice={1899} />
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>AMD Ryzen 7 7800X3D</span>
                    <PriceSmall usdPrice={449} />
                  </div>
                  <div className="flex justify-between">
                    <span>NVIDIA RTX 4070 Super</span>
                    <PriceSmall usdPrice={599} />
                  </div>
                  <div className="flex justify-between">
                    <span>32GB DDR5-6000</span>
                    <PriceSmall usdPrice={189} />
                  </div>
                  <div className="flex justify-between">
                    <span>MSI B650 Motherboard</span>
                    <PriceSmall usdPrice={179} />
                  </div>
                  <div className="flex justify-between">
                    <span>1TB NVMe SSD</span>
                    <PriceSmall usdPrice={89} />
                  </div>
                  <div className="flex justify-between">
                    <span>750W Gold PSU</span>
                    <PriceSmall usdPrice={129} />
                  </div>
                  <div className="flex justify-between">
                    <span>Mid-Tower Case</span>
                    <PriceSmall usdPrice={89} />
                  </div>
                  <div className="flex justify-between">
                    <span>CPU Cooler</span>
                    <PriceSmall usdPrice={79} />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
