'use client';

import { useState, useEffect, memo, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useToast } from '@/components/ui/use-toast';
import Link from 'next/link';
import { ArrowRight, Heart, Plus, Trash, Camera, Loader2 } from 'lucide-react';
import { useAuthStore, UserProfile } from '@/lib/store/auth-store';
import { FirestoreService } from '@/lib/services/firestore-service';
import { UserService } from '@/lib/services/user-service';
import { StorageService } from '@/lib/services/storage-service';
import { PCBuild } from '@/types/pc-build';

import { countryData } from '@/lib/utils/country-marketplace-data';

// List of countries from country data
const countries = Object.entries(countryData).map(([code, data]) => ({
  value: code.toLowerCase(),
  label: data.name
})).sort((a, b) => a.label.localeCompare(b.label));

const DashboardPage = memo(() => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const { user, updateUserProfile } = useAuthStore();
  const [activeTab, setActiveTab] = useState('builds');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [profileImage, setProfileImage] = useState<string | undefined>();
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [savedBuilds, setSavedBuilds] = useState<PCBuild[]>([]);
  const [favoriteBuilds, setFavoriteBuilds] = useState<PCBuild[]>([]);

  // Get the tab from query params
  const tabParam = searchParams.get('tab');

  // Set active tab based on query param
  useEffect(() => {
    if (tabParam && ['builds', 'favorites', 'settings'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Redirect if user is not signed in
  useEffect(() => {
    if (!user && !isLoading) {
      router.push('/auth/signin?redirect=/dashboard');
    }
  }, [user, isLoading, router]);

  // Fetch user data and builds
  useEffect(() => {
    const fetchData = async () => {
      if (!user) {
        setTimeout(() => {
          setIsLoading(false);
        }, 1000);
        return;
      }

      try {
        setIsLoading(true);

        // Fetch user profile
        const profile = await UserService.getProfileByUserId(user.uid);
        if (profile) {
          setUserProfile({
            firstName: profile.firstName ?? '',
            lastName: profile.lastName ?? '',
            email: profile.email ?? user.email ?? '',
            country: profile.country ?? '',
            photoURL: profile.photoURL ?? user.photoURL ?? undefined,
          });

          if (profile.photoURL) {
            setProfileImage(profile.photoURL);
          }
        } else {
          // Create default profile if none exists
          const nameParts = user.displayName?.split(' ') ?? ['', ''];
          setUserProfile({
            firstName: nameParts[0] ?? '',
            lastName: nameParts.slice(1).join(' ') ?? '',
            email: user.email ?? '',
            country: '',
            photoURL: user.photoURL ?? undefined,
          });
        }

        // Fetch user builds
        const builds = await FirestoreService.getUserBuilds(user.uid);
        setSavedBuilds(builds as PCBuild[]);

        // For now, favorites are empty
        setFavoriteBuilds([]);
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load user data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [user, toast]);

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setProfileImage(result);

        // Update user profile with new image
        if (userProfile) {
          setUserProfile({
            ...userProfile,
            photoURL: result,
          });
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle profile update
  const handleProfileUpdate = async () => {
    if (!user || !userProfile) return;

    try {
      setIsSaving(true);

      // Create a copy of the user profile to update
      const updatedProfile = { ...userProfile };

      // Check if the profile image is a data URL (newly uploaded)
      if (updatedProfile.photoURL?.startsWith('data:')) {
        try {
          // Upload the image to Firebase Storage
          const photoURL = await StorageService.uploadProfileImage(user.uid, updatedProfile.photoURL);
          updatedProfile.photoURL = photoURL;
        } catch (uploadError) {
          console.error('Error uploading profile image:', uploadError);
          toast({
            title: 'Image Upload Failed',
            description: 'Failed to upload profile image, but we will continue updating your profile.',
            variant: 'destructive',
          });
        }
      }

      // Update user profile in Firestore
      await UserService.createOrUpdateProfile(user.uid, updatedProfile);

      // Update user profile in Firebase Auth
      await updateUserProfile(updatedProfile);

      // Update local state
      setUserProfile(updatedProfile);
      setProfileImage(updatedProfile.photoURL);

      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toast({
        title: 'Error',
        description: 'Failed to update profile. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle build deletion
  const handleDeleteBuild = async (buildId: string) => {
    if (!user) return;

    try {
      await FirestoreService.deleteBuild(buildId);

      // Update local state
      setSavedBuilds(savedBuilds.filter(build => build.id !== buildId));

      toast({
        title: 'Build deleted',
        description: 'Your build has been deleted successfully.',
      });
    } catch (error) {
      console.error('Error deleting build:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete build. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Handle marking build as favorite
  const handleMarkAsFavorite = async (build: PCBuild) => {
    if (!user) return;

    try {
      // For now, just add to local favorites state
      // In a real implementation, you'd save this to Firestore
      const isAlreadyFavorite = favoriteBuilds.some(fav => fav.id === build.id);

      if (isAlreadyFavorite) {
        setFavoriteBuilds(favoriteBuilds.filter(fav => fav.id !== build.id));
        toast({
          title: 'Removed from favorites',
          description: 'Build has been removed from your favorites.',
        });
      } else {
        setFavoriteBuilds([...favoriteBuilds, build]);
        toast({
          title: 'Added to favorites',
          description: 'Build has been added to your favorites.',
        });
      }
    } catch (error) {
      console.error('Error updating favorites:', error);
      toast({
        title: 'Error',
        description: 'Failed to update favorites. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Format date
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return 'Unknown date';

    try {
      // Handle both Date objects and timestamp strings/numbers
      const dateObj = date instanceof Date ? date : new Date(date);

      // Check if the date is valid
      if (isNaN(dateObj.getTime())) {
        return 'Unknown date';
      }

      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Unknown date';
    }
  };

  return (
    <MainLayout>
      <div className="container max-w-7xl px-4 py-12 mx-auto">
        <div className="flex flex-col items-start gap-4 md:flex-row md:justify-between md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground mt-1">Manage your PC builds and account</p>
          </div>
          <Button asChild>
            <Link href="/build/new">
              <Plus className="mr-2 h-4 w-4" />
              New Build
            </Link>
          </Button>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : (
          <Tabs defaultValue={activeTab} className="w-full" onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3 max-w-md mb-8">
              <TabsTrigger value="builds">Saved Builds</TabsTrigger>
              <TabsTrigger value="favorites">Favorites</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="builds" className="mt-0">
              {savedBuilds.length > 0 ? (
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {savedBuilds.map((build) => (
                    <Card key={build.id} className="overflow-hidden">
                      <CardHeader className="p-6">
                        <CardTitle>{build.name}</CardTitle>
                        <CardDescription>
                          Created on {formatDate(build.createdAt)}
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="p-6 pt-0">
                        <div className="flex justify-between mb-2">
                          <span className="text-muted-foreground">Budget:</span>
                          <span>{build.budget}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Components:</span>
                          <span>{build.components.length}</span>
                        </div>
                      </CardContent>
                      <CardFooter className="p-6 pt-0 flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleMarkAsFavorite(build)}
                        >
                          <Heart className={`mr-2 h-4 w-4 ${favoriteBuilds.some(fav => fav.id === build.id) ? 'fill-current text-red-500' : ''}`} />
                          {favoriteBuilds.some(fav => fav.id === build.id) ? 'Favorited' : 'Mark as Favorite'}
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDeleteBuild(build.id as string)}
                        >
                          <Trash className="mr-2 h-4 w-4" />
                          Delete
                        </Button>
                      </CardFooter>
                    </Card>
                  ))}

                  <Card className="overflow-hidden border-dashed">
                    <CardContent className="p-6 flex flex-col items-center justify-center h-full min-h-[200px]">
                      <Plus className="h-8 w-8 text-muted-foreground mb-4" />
                      <p className="text-muted-foreground text-center mb-4">Create a new PC build</p>
                      <Button asChild>
                        <Link href="/build/new">
                          Start Building
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <Card className="overflow-hidden">
                  <CardContent className="p-12 flex flex-col items-center justify-center">
                    <div className="rounded-full bg-primary/10 p-4 mb-4">
                      <Plus className="h-8 w-8 text-primary" />
                    </div>
                    <h3 className="text-xl font-medium mb-2">No builds yet</h3>
                    <p className="text-muted-foreground text-center mb-6">
                      You haven&apos;t created any PC builds yet. Start building your first PC now!
                    </p>
                    <Button asChild>
                      <Link href="/build/new">
                        Start Building
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="favorites" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Favorites</CardTitle>
                  <CardDescription>Builds and components you&apos;ve marked as favorites</CardDescription>
                </CardHeader>
                <CardContent>
                  {favoriteBuilds.length > 0 ? (
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      {favoriteBuilds.map((build) => (
                        <Card key={build.id} className="overflow-hidden">
                          <CardHeader className="p-6">
                            <CardTitle>{build.name}</CardTitle>
                            <CardDescription>
                              Created on {formatDate(build.createdAt)}
                            </CardDescription>
                          </CardHeader>
                          <CardContent className="p-6 pt-0">
                            <div className="flex justify-between mb-2">
                              <span className="text-muted-foreground">Budget:</span>
                              <span>{build.budget}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">Components:</span>
                              <span>{build.components.length}</span>
                            </div>
                          </CardContent>
                          <CardFooter className="p-6 pt-0 flex justify-center">
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() => handleMarkAsFavorite(build)}
                              className="w-full"
                            >
                              <Heart className="mr-2 h-4 w-4 fill-current" />
                              Remove from Favorites
                            </Button>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center py-8 text-muted-foreground">You haven&apos;t saved any favorites yet.</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle>Account Settings</CardTitle>
                  <CardDescription>Manage your account preferences</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {userProfile ? (
                    <div className="space-y-6">
                      {/* Profile Image */}
                      <div className="flex justify-center mb-6">
                        <div className="relative">
                          <Avatar className="h-24 w-24">
                            <AvatarImage className="object-cover" src={userProfile.photoURL ?? profileImage ?? ''} alt={userProfile.firstName + ' ' + userProfile.lastName} />
                            <AvatarFallback className="text-lg">
                              {userProfile.firstName && userProfile.lastName
                                ? `${userProfile.firstName[0]}${userProfile.lastName[0]}`
                                : 'U'}
                            </AvatarFallback>
                          </Avatar>
                          <label
                            htmlFor="profile-image"
                            className="absolute bottom-0 right-0 bg-primary text-primary-foreground rounded-full p-2 cursor-pointer"
                          >
                            <Camera className="h-4 w-4" />
                          </label>
                          <input
                            id="profile-image"
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleImageUpload}
                          />
                        </div>
                      </div>

                      {/* User Information */}
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-2">
                          <Label htmlFor="firstName">First Name</Label>
                          <Input
                            id="firstName"
                            value={userProfile.firstName}
                            onChange={(e) => setUserProfile({ ...userProfile, firstName: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="lastName">Last Name</Label>
                          <Input
                            id="lastName"
                            value={userProfile.lastName}
                            onChange={(e) => setUserProfile({ ...userProfile, lastName: e.target.value })}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          value={userProfile.email}
                          disabled
                        />
                        <p className="text-sm text-muted-foreground">Your email address cannot be changed</p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="country">Country</Label>
                        <Select
                          value={userProfile.country}
                          onValueChange={(value: string) => setUserProfile({ ...userProfile, country: value })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select your country" />
                          </SelectTrigger>
                          <SelectContent>
                            {countries.map((country) => (
                              <SelectItem key={country.value} value={country.value}>
                                {country.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          onClick={handleProfileUpdate}
                          disabled={isSaving}
                        >
                          {isSaving && (
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          )}
                          Save Changes
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-center py-8 text-muted-foreground">Loading user profile...</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </MainLayout>
  );
});

DashboardPage.displayName = 'DashboardPage';

function DashBoardWithSuspense() {
  return (
    <Suspense>
      <DashboardPage />
    </Suspense>
  );
}

export default DashBoardWithSuspense;
