import type { Viewport, Metadata } from "next";
import { <PERSON><PERSON><PERSON>, Source_Code_Pro } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "next-themes";
import { Toaster } from "@/components/ui/toaster";
import StoreProvider from "@/components/providers/store-provider";

const poppinsSans = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

const sourceCodePro = Source_Code_Pro({
  variable: "--font-source-code-pro",
  subsets: ["latin"],
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: "#ffffff",
};

export const metadata: Metadata = {
  applicationName: "DIYPC - Build Your Perfect PC",
  title: "DIYPC - Build Your Perfect PC",
  description: "AI-powered PC building assistant to help you create your perfect custom PC",
  metadataBase: new URL("https://diypc.vercel.app"),
  authors: [{ name: "<PERSON><PERSON><PERSON> <PERSON>", url: "https://coderhd.github.io" }],
  openGraph: {
    siteName: "DIYPC - Build Your Perfect PC",
    type: "website",
    locale: "en_US",
    url: "https://diypc.vercel.app",
    title: "DIYPC - Build Your Perfect PC",
    description: "AI-powered PC building assistant to help you create your perfect custom PC",
    images: [
      {
        url: "https://diypc.vercel.app/icon.svg",
        width: 512,
        height: 512,
        alt: "DIYPC Logo",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "DIYPC - Build Your Perfect PC",
    description: "AI-powered PC building assistant to help you create your perfect custom PC",
    site: "@harshdave1095",
    creator: "@harshdave1095",
    images: [
      {
        url: "https://diypc.vercel.app/icon.svg",
        width: 512,
        height: 512,
        alt: "DIYPC Logo",
      },
    ],
  },
  robots: {
    index: true,
    follow: true,
    "max-image-preview": "large",
    "max-snippet": -1,
    "max-video-preview": -1,
    googleBot: "index, follow",
  },
  appleWebApp: {
    title: "DIYPC - Build Your Perfect PC",
    capable: true,
    statusBarStyle: "black-translucent",
  },
  keywords: [
    "PC building",
    "Custom PC",
    "AI PC building",
    "DIY PC",
    "PC builder",
    "PC customization",
    "PC recommendation",
    "PC configuration",
    "PC assembly",
    "PC parts",
    "PC components",
    "PC hardware",
    "PC software",
    "PC accessories",
    "PC cooling",
    "PC storage",
    "PC power supply",
    "PC case",
    "PC cooling",
    "PC monitoring",
    "PC optimization",
    "PC troubleshooting",
    "PC upgrade",
    "PC repair",
    "PC maintenance",
    "PC security",
    "PC performance",
    "PC compatibility",
    "PC build",
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="apple-touch-icon" href="/apple-icon.png" type="image/png" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
      </head>
      <body
        className={`${poppinsSans.variable} ${sourceCodePro.variable} antialiased`}
      >
        <ThemeProvider defaultTheme="system" storageKey="diypc-theme" enableSystem attribute="class" enableColorScheme>
          <StoreProvider>
            {children}
            <Toaster />
          </StoreProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
