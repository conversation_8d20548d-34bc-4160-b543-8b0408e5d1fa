'use client';

import { useState, useEffect } from 'react';
import { AdminLayout } from '@/components/layout/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Plus, Trash2, UserCheck, UserX } from 'lucide-react';
import { AdminService } from '@/lib/services/admin-service';
import { AdminUser, AdminPermission, adminRoles } from '@/types/admin';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuthStore } from '@/lib/store/auth-store';
import { UserService } from '@/lib/services/user-service';

export default function AdminUsersPage() {
  const { toast } = useToast();
  const { user } = useAuthStore();
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);

  // Form state
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserDisplayName, setNewUserDisplayName] = useState('');
  const [newUserRole, setNewUserRole] = useState<'admin' | 'super-admin'>('admin');
  const [newUserPermissions, setNewUserPermissions] = useState<AdminPermission[]>([]);

  useEffect(() => {
    loadAdminUsers();
  }, []);

  useEffect(() => {
    // Update permissions when role changes
    if (newUserRole && adminRoles[newUserRole]) {
      setNewUserPermissions(adminRoles[newUserRole].permissions);
    }
  }, [newUserRole]);

  const loadAdminUsers = async () => {
    try {
      setIsLoading(true);
      const users = await AdminService.getAllAdminUsers();
      setAdminUsers(users);
    } catch (error) {
      console.error('Error loading admin users:', error);
      toast({
        title: 'Error',
        description: 'Failed to load admin users',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getUserFromFirestore = async (email: string) => {
    try {
      const user = await UserService.getProfileByEmail(email);
      return user;
    } catch (error) {
      console.error('Error checking user existence:', error);
      return null;
    }
  };

  const handleAddUser = async () => {
    if (!newUserEmail.trim()) {
      toast({
        title: 'Error',
        description: 'Email is required',
        variant: 'destructive',
      });
      return;
    }

    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to add admin users',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const firestoreUser = await getUserFromFirestore(newUserEmail);
      if (!firestoreUser) {
        toast({
          title: 'Error',
          description: 'User does not exist',
          variant: 'destructive',
        });
        return;
      }

      // For now, we'll use the email as the userId since we don't have user lookup
      // In a real implementation, you'd want to look up the user by email first
      const adminData = {
        userId: firestoreUser.userId, // This should be the actual Firebase user ID
        email: newUserEmail,
        displayName: newUserDisplayName || undefined,
        role: newUserRole,
        permissions: newUserPermissions,
        isActive: true,
      };

      await AdminService.addAdminUser(adminData, user.uid);

      toast({
        title: 'Success',
        description: 'Admin user added successfully',
      });

      // Reset form
      setNewUserEmail('');
      setNewUserDisplayName('');
      setNewUserRole('admin');
      setNewUserPermissions(adminRoles.admin.permissions);
      setShowAddForm(false);

      // Reload users
      await loadAdminUsers();
    } catch (error) {
      console.error('Error adding admin user:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to add admin user',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleToggleUserStatus = async (adminUser: AdminUser) => {
    if (!adminUser.id) return;

    try {
      await AdminService.updateAdminUser(adminUser.id, {
        isActive: !adminUser.isActive,
      });

      toast({
        title: 'Success',
        description: `Admin user ${adminUser.isActive ? 'deactivated' : 'activated'} successfully`,
      });

      await loadAdminUsers();
    } catch (error) {
      console.error('Error updating admin user:', error);
      toast({
        title: 'Error',
        description: 'Failed to update admin user',
        variant: 'destructive',
      });
    }
  };

  const handleRemoveUser = async (adminUser: AdminUser) => {
    if (!adminUser.id) return;

    try {
      await AdminService.removeAdminUser(adminUser.id);

      toast({
        title: 'Success',
        description: 'Admin user removed successfully',
      });

      await loadAdminUsers();
    } catch (error) {
      console.error('Error removing admin user:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove admin user',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="container max-w-6xl px-4 py-8 mx-auto">
          <div className="text-center">Loading admin users...</div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container max-w-6xl px-4 py-8 mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Admin Users</h1>
            <p className="text-muted-foreground">Manage admin users and their permissions</p>
          </div>
          <Button onClick={() => setShowAddForm(!showAddForm)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Admin User
          </Button>
        </div>

        {showAddForm && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Add New Admin User</CardTitle>
              <CardDescription>
                Add a new admin user with specific permissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={newUserEmail}
                    onChange={(e) => setNewUserEmail(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="displayName">Display Name</Label>
                  <Input
                    id="displayName"
                    placeholder="John Doe"
                    value={newUserDisplayName}
                    onChange={(e) => setNewUserDisplayName(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={newUserRole} onValueChange={(value) => setNewUserRole(value as 'admin' | 'super-admin')}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(adminRoles).map(([key, role]) => (
                      <SelectItem key={key} value={key}>
                        {role.name} - {role.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex gap-4">
                <Button onClick={handleAddUser} disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add User'}
                </Button>
                <Button variant="outline" onClick={() => setShowAddForm(false)}>
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid gap-4">
          {adminUsers.map((adminUser) => (
            <Card key={adminUser.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div>
                      <h3 className="font-semibold">{adminUser.displayName || adminUser.email}</h3>
                      <p className="text-sm text-muted-foreground">{adminUser.email}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={adminUser.role === 'super-admin' ? 'default' : 'secondary'}>
                          {adminRoles[adminUser.role]?.name || adminUser.role}
                        </Badge>
                        <Badge variant={adminUser.isActive ? 'default' : 'destructive'}>
                          {adminUser.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleToggleUserStatus(adminUser)}
                    >
                      {adminUser.isActive ? (
                        <>
                          <UserX className="h-4 w-4 mr-2" />
                          Deactivate
                        </>
                      ) : (
                        <>
                          <UserCheck className="h-4 w-4 mr-2" />
                          Activate
                        </>
                      )}
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={() => handleRemoveUser(adminUser)}
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Remove
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {adminUsers.length === 0 && (
            <Card>
              <CardContent className="p-8 text-center">
                <p className="text-muted-foreground">No admin users found</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </AdminLayout>
  );
}
