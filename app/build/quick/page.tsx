'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { motion, AnimatePresence } from 'framer-motion';
import { Zap, Loader2, ArrowRight, HelpCircle, Save } from 'lucide-react';
import { PCBuild, UseCase } from '@/types/pc-build';
import { VertexAIService } from '@/lib/services/vertex-ai-service';
import { ComponentExplanationService } from '@/lib/services/component-explanation-service';
import { BuildSummary } from '@/components/build/build-summary';
import { ComponentSelector } from '@/components/build/component-selector';
import { useBuildStore } from '@/lib/store/build-store';
import { useToast } from '@/components/ui/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { useAuthStore } from '@/lib/store/auth-store';
import { PriceSmall } from '@/components/ui/price';

interface QuickBuildPreferences {
  budget: number;
  useCase: UseCase[];
  cpuBrand?: 'Intel' | 'AMD';
  gpuBrand?: 'NVIDIA' | 'AMD';
  ramType?: 'DDR4' | 'DDR5';
  casePreference?: 'Compact' | 'Mid Tower' | 'Full Tower';
  psuEfficiency?: 'Bronze' | 'Gold' | 'Platinum';
}

export default function QuickBuildPage() {
  // Use the auth store
  const user = useAuthStore((state) => state.user);
  const [preferences, setPreferences] = useState<QuickBuildPreferences>({
    budget: 1000,
    useCase: [],
    cpuBrand: undefined,
    gpuBrand: undefined,
    ramType: undefined,
    casePreference: undefined,
    psuEfficiency: undefined,
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedBuild, setGeneratedBuild] = useState<PCBuild | null>(null);
  const [currentStep, setCurrentStep] = useState<'preferences' | 'build' | 'components'>('preferences');
  const [explanationDialog, setExplanationDialog] = useState<{ open: boolean; component: string; explanation: string }>({
    open: false,
    component: '',
    explanation: ''
  });
  const [componentExplanations, setComponentExplanations] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [buildName, setBuildName] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  const { setCurrentBuild, saveBuild, currentBuild } = useBuildStore();
  const { toast } = useToast();

  const useCaseOptions = [
    { value: 'gaming', label: 'Gaming' },
    { value: 'content', label: 'Content Creation' },
    { value: 'work', label: 'Work & Productivity' },
    { value: 'everyday use', label: 'Everyday Use' },
  ];

  // Budget-based component filtering
  const getAvailableOptions = () => {
    const budget = preferences.budget;

    return {
      ramType: budget >= 800 ? ['DDR4', 'DDR5'] : ['DDR4'],
      psuEfficiency: budget >= 600 ? ['Bronze', 'Gold', 'Platinum'] : budget >= 400 ? ['Bronze', 'Gold'] : ['Bronze'],
      casePreference: ['Compact', 'Mid Tower', 'Full Tower'],
    };
  };

  const handleUseCaseChange = (useCase: UseCase, checked: boolean) => {
    setPreferences(prev => ({
      ...prev,
      useCase: checked
        ? [...prev.useCase, useCase]
        : prev.useCase.filter(uc => uc !== useCase)
    }));
  };

  const generateBuild = async () => {
    if (preferences.useCase.length === 0) {
      toast({
        title: 'Use case required',
        description: 'Please select at least one use case for your PC.',
        variant: 'destructive',
      });
      return;
    }

    setIsGenerating(true);

    try {
      const budgetRange = getBudgetRange(preferences.budget);
      const primaryUseCase = preferences.useCase[0];

      // Create requirements string based on preferences
      const requirements = [
        preferences.cpuBrand && `CPU: Prefer ${preferences.cpuBrand}`,
        preferences.gpuBrand && `GPU: Prefer ${preferences.gpuBrand}`,
        preferences.ramType && `RAM: Use ${preferences.ramType}`,
        preferences.casePreference && `Case: ${preferences.casePreference} form factor`,
        preferences.psuEfficiency && `PSU: ${preferences.psuEfficiency}+ efficiency`,
        preferences.useCase.length > 1 && `Additional use cases: ${preferences.useCase.slice(1).join(', ')}`
      ].filter(Boolean).join('. ');

      const build = await VertexAIService.generateBuildRecommendation(
        primaryUseCase,
        budgetRange,
        requirements
      );

      setGeneratedBuild(build);
      setCurrentBuild(build);
      setCurrentStep('build');
    } catch (error) {
      console.error('Error generating build:', error);
      toast({
        title: 'Error',
        description: 'Failed to generate build. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleSaveBuild = async () => {
    if (!currentBuild) return;

    try {
      setIsSaving(true);

      // Generate a temporary user ID if not authenticated
      const userId = user?.uid ?? ('guest-' + Math.random().toString(36).substring(2, 15));

      // Use the build's use case for the name
      const useCaseText = currentBuild.useCase.charAt(0).toUpperCase() + currentBuild.useCase.slice(1);

      // Save build to Firebase using the store's saveBuild function
      await saveBuild(userId, {
        ...currentBuild,
        name: buildName || `${useCaseText} Build (${currentBuild.budget})`
      });

      toast({
        title: 'Success',
        description: 'Your build has been saved successfully.',
      });

      setShowSaveDialog(false);

    } catch (error) {
      console.error('Error saving build:', error);
      toast({
        title: 'Error',
        description: 'Failed to save build. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getBudgetRange = (budget: number): string => {
    if (budget <= 600) return '$400-$600';
    if (budget <= 1000) return '$600-$1000';
    if (budget <= 1500) return '$1000-$1500';
    if (budget <= 2000) return '$1500-$2000';
    return '$2000+';
  };

  const explainComponent = async (componentType: string, componentName: string) => {
    if (componentExplanations[componentType]) {
      setExplanationDialog({ open: true, component: componentType, explanation: componentExplanations[componentType] });
      return;
    }

    setExplanationDialog({ open: true, component: componentType, explanation: 'Just a moment...' });

    try {
      const buildContext = {
        budget: preferences.budget,
        useCase: preferences.useCase,
        preferences: {
          cpuBrand: preferences.cpuBrand || 'Any',
          gpuBrand: preferences.gpuBrand || 'Any',
          ramType: preferences.ramType || 'Any',
          casePreference: preferences.casePreference || 'Any',
          psuEfficiency: preferences.psuEfficiency || 'Any',
        }
      };

      const explanation = await ComponentExplanationService.explainComponentChoice(
        componentType,
        componentName,
        buildContext
      );

      setExplanationDialog(prev => ({ ...prev, explanation }));
      setComponentExplanations(prev => ({ ...prev, [componentType]: explanation }));
    } catch (error) {
      console.error('Error explaining component:', error);
      setExplanationDialog(prev => ({ ...prev, explanation: 'Failed to load explanation. Please try again.' }));
    }
  };

  const availableOptions = getAvailableOptions();

  return (
    <MainLayout>
      <div className="container max-w-7xl px-4 py-12 mx-auto">
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-center gap-2 mb-4">
            <Zap className="h-8 w-8 text-primary" />
            <h1 className="text-4xl font-bold">Quick Build</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Get a PC build recommendation in seconds by selecting your preferences
          </p>
        </motion.div>

        <AnimatePresence mode="wait">
          {currentStep === 'preferences' && (
            <motion.div
              key="preferences"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="max-w-2xl mx-auto">
                <CardHeader>
                  <CardTitle>Build Preferences</CardTitle>
                  <CardDescription>
                    Configure your preferences to get a personalized PC build recommendation
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Budget Slider */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">
                      Budget: ${preferences.budget} <span className="text-red-500">*</span>
                    </Label>
                    <Slider
                      value={[preferences.budget]}
                      onValueChange={(value) => setPreferences(prev => ({ ...prev, budget: value[0] }))}
                      max={3000}
                      min={400}
                      step={50}
                      className="w-full"
                    />
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>$400</span>
                      <span>$3000+</span>
                    </div>
                  </div>

                  {/* Use Case Selection */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">
                      Use Case <span className="text-red-500">*</span>
                    </Label>
                    <div className="grid grid-cols-2 gap-3">
                      {useCaseOptions.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <Checkbox
                            id={option.value}
                            checked={preferences.useCase.includes(option.value as UseCase)}
                            onCheckedChange={(checked) =>
                              handleUseCaseChange(option.value as UseCase, checked as boolean)
                            }
                          />
                          <Label htmlFor={option.value} className="text-sm font-normal">
                            {option.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* CPU Brand Preference */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">CPU Brand Preference (Optional)</Label>
                    <Select
                      value={preferences.cpuBrand}
                      onValueChange={(value) =>
                        setPreferences(prev => ({ ...prev, cpuBrand: value as 'Intel' | 'AMD' || undefined }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="No preference" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* <SelectItem value="">No preference</SelectItem> */}
                        <SelectItem value="Intel">Intel</SelectItem>
                        <SelectItem value="AMD">AMD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* GPU Brand Preference */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">GPU Brand Preference (Optional)</Label>
                    <Select
                      value={preferences.gpuBrand}
                      onValueChange={(value) =>
                        setPreferences(prev => ({ ...prev, gpuBrand: value as 'NVIDIA' | 'AMD' || undefined }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="No preference" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* <SelectItem value="">No preference</SelectItem> */}
                        <SelectItem value="NVIDIA">NVIDIA</SelectItem>
                        <SelectItem value="AMD">AMD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* RAM Type */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">RAM Type (Optional)</Label>
                    <Select
                      value={preferences.ramType}
                      onValueChange={(value) =>
                        setPreferences(prev => ({ ...prev, ramType: value as 'DDR4' | 'DDR5' || undefined }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="No preference" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* <SelectItem value="">No preference</SelectItem> */}
                        {availableOptions.ramType.map(type => (
                          <SelectItem key={type} value={type}>{type}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Case Preference */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Case Preference (Optional)</Label>
                    <Select
                      value={preferences.casePreference}
                      onValueChange={(value) =>
                        setPreferences(prev => ({ ...prev, casePreference: (value as 'Compact' | 'Mid Tower' | 'Full Tower') || undefined }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="No preference" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* <SelectItem value="">No preference</SelectItem> */}
                        {availableOptions.casePreference.map(pref => (
                          <SelectItem key={pref} value={pref}>{pref}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* PSU Efficiency */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">PSU Efficiency (Optional)</Label>
                    <Select
                      value={preferences.psuEfficiency}
                      onValueChange={(value) =>
                        setPreferences(prev => ({ ...prev, psuEfficiency: (value as 'Bronze' | 'Gold' | 'Platinum') || undefined }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="No preference" />
                      </SelectTrigger>
                      <SelectContent>
                        {/* <SelectItem value="">No preference</SelectItem> */}
                        {availableOptions.psuEfficiency.map(eff => (
                          <SelectItem key={eff} value={eff}>{eff}+ Efficiency</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Button
                    onClick={generateBuild}
                    disabled={isGenerating || preferences.useCase.length === 0}
                    className="w-full"
                    size="lg"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Generating Build...
                      </>
                    ) : (
                      <>
                        <Zap className="mr-2 h-4 w-4" />
                        Generate Quick Build
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          )}

          {currentStep === 'build' && generatedBuild && (
            <motion.div
              key="build"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
              className="space-y-6"
            >
              <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep('preferences')}
                >
                  ← Back to Preferences
                </Button>
                <Button
                  onClick={() => setCurrentStep('components')}
                >
                  Customize Components
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>

              <div className="grid lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {generatedBuild.components.map((component) => (
                    <Card key={component.type}>
                      <CardContent className="p-4">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h3 className="font-medium">{component.type}</h3>
                            <p className="text-sm text-muted-foreground">{component.name}</p>
                            <PriceSmall usdPrice={component.price} />
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => explainComponent(component.type, component.name)}
                          >
                            <HelpCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                <BuildSummary
                  build={generatedBuild}
                  onSave={() => setShowSaveDialog(true)}
                />
              </div>
            </motion.div>
          )}

          {currentStep === 'components' && generatedBuild && (
            <motion.div
              key="components"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="mb-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep('build')}
                >
                  ← Back to Build Summary
                </Button>
              </div>

              <ComponentSelector
                build={generatedBuild}
              // onComponentChange={}
              />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Component Explanation Dialog */}
        <Dialog open={explanationDialog.open} onOpenChange={(open) => setExplanationDialog(prev => ({ ...prev, open }))}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Why {generatedBuild?.components.find(c => c.type === explanationDialog.component)?.name}?</DialogTitle>
              <DialogDescription>
                Component explanation
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              <p>{explanationDialog.explanation}</p>
            </div>
          </DialogContent>
        </Dialog>

        {/* Save Build Dialog */}
        <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Save Your Build</DialogTitle>
              <DialogDescription>
                Give your build a name to save it to your account
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4 space-y-4">
              <div className="space-y-2">
                <label htmlFor="build-name" className="text-sm font-medium">
                  Build Name
                </label>
                <Input
                  id="build-name"
                  value={buildName}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setBuildName(e.target.value)}
                  placeholder="My Awesome Gaming PC"
                />
              </div>
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveBuild} disabled={isSaving}>
                <Save className="mr-2 h-4 w-4" />
                Save Build
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
}
