'use client';

import { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/main-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { motion } from 'framer-motion';
import { Save, Gamepad2, PenTool, Briefcase, Loader2 } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ComponentSelector } from '@/components/build/component-selector';
import { useBuildStore } from '@/lib/store/build-store';
import { PCBuild, UseCase, RecommendedBuilds } from '@/types/pc-build';
import { PriceLarge, PriceInline } from '@/components/ui/price';
import { PurchaseLinksCompact } from '@/components/build/purchase-links';
import { useAuthStore } from '@/lib/store/auth-store';
import { UserService } from '@/lib/services/user-service';
import { CountryCode } from '@/types/affiliate-links';
import { useToast } from '@/hooks/use-toast';

export default function RecommendationsPage() {
  const [selectedCategory, setSelectedCategory] = useState<UseCase>('gaming');
  const [selectedBuild, setSelectedBuild] = useState<PCBuild | null>(null);
  const [showBuildDetails, setShowBuildDetails] = useState(false);
  const [userCountry, setUserCountry] = useState<CountryCode>('US'); // Default to US
  const [isLoadingSave, setIsLoadingSave] = useState(false);

  // Use the build store for recommended builds with proper types
  const recommendedBuilds = useBuildStore(state => state.recommendedBuilds);
  const isLoading = useBuildStore(state => state.isLoadingRecommendedBuilds);
  const loadRecommendedBuilds = useBuildStore(state => state.loadRecommendedBuilds);
  const { user } = useAuthStore();
  const { toast } = useToast();

  useEffect(() => {
    const getUserCountry = async () => {
      if (user) {
        try {
          const profile = await UserService.getProfileByUserId(user.uid);
          if (profile?.country) {
            setUserCountry(profile.country.toUpperCase() as CountryCode);
          }
        } catch (error) {
          console.error('Error getting user profile:', error);
        }
      }
    };

    getUserCountry();
  }, [user]);

  const handleViewBuildDetails = (build: PCBuild) => {
    setSelectedBuild(build);
    setShowBuildDetails(true);
  };

  const { saveBuild } = useBuildStore();

  const handleSaveBuild = async (build: PCBuild) => {
    if (!user) {
      // Handle unauthenticated user - could show login prompt
      toast({
        title: 'Authentication required',
        description: 'Please sign in to save builds.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoadingSave(true);
      await saveBuild(user.uid, build);
      // Show success message or redirect to dashboard
      toast({
        title: 'Success',
        description: 'Your build has been saved successfully.',
      });
      setIsLoadingSave(false);
    } catch (error) {
      console.error('Error saving build:', error);
      // Show error message
      toast({
        title: 'Error',
        description: 'Failed to save build. Please try again.',
        variant: 'destructive',
      });
      setIsLoadingSave(false);
    }
  };

  const categoryIcons = {
    gaming: Gamepad2,
    content: PenTool,
    work: Briefcase,
  };

  // This function is no longer needed as we use the PurchaseLinks component

  return (
    <MainLayout>
      <div className="container max-w-7xl px-4 py-12 mx-auto">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">AI-Recommended Builds</h1>
            <p className="text-muted-foreground mt-1">Pre-configured PC builds optimized for different use cases</p>
          </div>
        </div>

        <Tabs
          defaultValue="gaming"
          value={selectedCategory}
          className="w-full"
          onValueChange={(value) => setSelectedCategory(value as UseCase)}
        >
          <TabsList className="grid w-full grid-rows-2 grid-cols-2 sm:grid-rows-1 sm:grid-cols-3 mb-8">
            <TabsTrigger value="gaming">Gaming</TabsTrigger>
            <TabsTrigger value="content">Content Creation</TabsTrigger>
            <TabsTrigger value="work">Work & Productivity</TabsTrigger>
          </TabsList>

          {(['gaming', 'content', 'work'] as UseCase[]).map((category) => (
            <TabsContent key={category} value={category} className="mt-0">
              {isLoading ? (
                // Loading skeleton
                <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                  {[1, 2, 3].map((i) => (
                    <Card key={i} className="h-full flex flex-col">
                      <CardHeader>
                        <div className="flex items-center gap-2 mb-2">
                          <Skeleton className="h-8 w-8 rounded-full" />
                          <Skeleton className="h-6 w-40" />
                        </div>
                        <Skeleton className="h-4 w-full mt-2" />
                      </CardHeader>
                      <CardContent className="flex-grow">
                        <Skeleton className="h-8 w-24 mb-4" />
                        <div className="space-y-2">
                          {[1, 2, 3, 4].map((j) => (
                            <div key={j} className="flex justify-between">
                              <Skeleton className="h-4 w-20" />
                              <Skeleton className="h-4 w-32" />
                            </div>
                          ))}
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between">
                        <Skeleton className="h-9 w-24" />
                        <Skeleton className="h-9 w-24" />
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <RenderCategoryContent
                  category={category}
                  recommendedBuilds={recommendedBuilds}
                  categoryIcons={categoryIcons}
                  handleViewBuildDetails={handleViewBuildDetails}
                  handleSaveBuild={handleSaveBuild}
                  loadRecommendedBuilds={loadRecommendedBuilds}
                  isLoadingSave={isLoadingSave}
                />
              )}
            </TabsContent>
          ))}
        </Tabs>

        {/* Build Details Dialog */}
        <Dialog open={showBuildDetails} onOpenChange={setShowBuildDetails}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>{selectedBuild?.name}</DialogTitle>
              <DialogDescription>
                {selectedBuild?.description ?? `${selectedBuild?.useCase} PC build with a budget of ${selectedBuild?.budget}`}
              </DialogDescription>
            </DialogHeader>
            <div className="mt-4 max-h-[70vh] overflow-y-auto">
              {selectedBuild && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="text-sm text-muted-foreground">Total Price</p>
                      <PriceLarge usdPrice={selectedBuild.totalPrice} />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Use Case</p>
                      <p className="font-medium">{selectedBuild.useCase.charAt(0).toUpperCase() + selectedBuild.useCase.slice(1)}</p>
                    </div>
                  </div>

                  <ComponentSelector build={selectedBuild} readOnly />

                  <div className="space-y-2">
                    <h3 className="font-medium">Buy Components</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedBuild.components.filter(component => component.price > 0).map((component) => (
                        <Card key={component.type} className="overflow-hidden">
                          <CardHeader className="p-3 pb-0">
                            <CardTitle className="text-sm">{component.type}</CardTitle>
                            <CardDescription className="text-xs truncate">{component.name}</CardDescription>
                          </CardHeader>
                          <CardContent className="p-3 pt-2">
                            <PurchaseLinksCompact
                              componentType={component.type}
                              componentName={component.name}
                              componentBrand={component.brand}
                              componentModel={component.model}
                              userCountry={userCountry}
                              className="text-xs"
                            />
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-end gap-2 mt-4">
              <Button
                variant="outline"
                onClick={() => setShowBuildDetails(false)}
              >
                Close
              </Button>
              <Button
                onClick={() => handleSaveBuild(selectedBuild!)}
                disabled={isLoadingSave}
              >
                {isLoadingSave ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Build
                  </>
                )}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </MainLayout>
  );
}

// Helper components defined outside the main component
const RenderCategoryContent = ({
  category,
  recommendedBuilds,
  categoryIcons,
  handleViewBuildDetails,
  handleSaveBuild,
  loadRecommendedBuilds,
  isLoadingSave
}: {
  category: UseCase;
  recommendedBuilds: RecommendedBuilds;
  categoryIcons: Record<UseCase, React.ElementType>;
  handleViewBuildDetails: (build: PCBuild) => void;
  handleSaveBuild: (build: PCBuild) => void;
  loadRecommendedBuilds: (forceRefresh?: boolean) => Promise<void>;
  isLoadingSave: boolean;
}) => {
  if (recommendedBuilds[category]?.length > 0) {
    return (
      <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
        {recommendedBuilds[category].map((build: PCBuild, index: number) => {
          const CategoryIcon = categoryIcons[category];
          const totalPrice = typeof build.totalPrice === 'number'
            ? build.totalPrice
            : 0;

          return (
            <motion.div
              key={build.id ?? index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="h-full flex flex-col">
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <CategoryIcon className="h-4 w-4 text-primary" />
                    </div>
                    <CardTitle>{build.name}</CardTitle>
                  </div>
                  <CardDescription>{build.description}</CardDescription>
                </CardHeader>
                <CardContent className="flex-grow">
                  <div className="mb-4">
                    <PriceInline usdPrice={totalPrice} className="text-xl font-bold" />
                  </div>
                  <div className="space-y-2">
                    {build.components.slice(0, 4).map((component) => (
                      <div key={component.type} className="flex justify-between">
                        <span className="text-muted-foreground">{component.type}:</span>
                        <span className="font-medium truncate ml-2 text-right max-w-[60%]">{component.name}</span>
                      </div>
                    ))}
                    {build.components.length > 4 && (
                      <div className="text-center text-sm text-muted-foreground mt-2">
                        <Button
                          variant="link"
                          size="sm"
                          className="p-0 h-auto"
                          onClick={() => handleViewBuildDetails(build)}
                        >
                          View all {build.components.length} components
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-center">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleSaveBuild(build)}
                    disabled={isLoadingSave}
                    className="w-full"
                  >
                    {isLoadingSave ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Build
                      </>
                    )}
                  </Button>
                </CardFooter>
              </Card>
            </motion.div>
          );
        })}
      </div>
    );
  } else {
    return (
      <div className="text-center py-12">
        <p className="text-muted-foreground mb-4">No recommended builds found for this category.</p>
        <Button onClick={() => loadRecommendedBuilds()}>
          <Loader2 className="mr-2 h-4 w-4" />
          Refresh Recommendations
        </Button>
      </div>
    );
  }
};
