'use client';

import { useState, useEffect, useCallback } from 'react';
import { CurrencyService } from '@/lib/services/currency-service';
import { useAuthStore } from '@/lib/store/auth-store';
import { UserService } from '@/lib/services/user-service';

/**
 * Hook for currency conversion and formatting
 */
export function useCurrency() {
  const { user } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userCountry, setUserCountry] = useState<string>('US');

  // Fetch user's country from profile
  useEffect(() => {
    const fetchUserCountry = async () => {
      if (!user) {
        setUserCountry('US');
        return;
      }

      try {
        const profile = await UserService.getProfileByUserId(user.uid);
        if (profile?.country) {
          setUserCountry(profile.country.toUpperCase());
        } else {
          setUserCountry('US');
        }
      } catch (error) {
        console.error('Error fetching user profile for currency:', error);
        setUserCountry('US');
      }
    };

    fetchUserCountry();
  }, [user]);

  // Get user's country from profile or default to US
  const getUserCountry = useCallback(() => {
    return userCountry;
  }, [userCountry]);

  /**
   * Convert USD price to user's currency
   * @param usdPrice Price in USD
   * @returns Converted price
   */
  const convertPrice = useCallback(async (usdPrice: number): Promise<number> => {
    try {
      setIsLoading(true);
      setError(null);

      const userCountry = getUserCountry();
      const currencyInfo = CurrencyService.getCurrencyForCountry(userCountry);
      const convertedPrice = await CurrencyService.convertFromUSD(usdPrice, currencyInfo.currency);

      return convertedPrice;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Currency conversion failed';
      setError(errorMessage);
      return usdPrice; // Fallback to original price
    } finally {
      setIsLoading(false);
    }
  }, [getUserCountry]);

  /**
   * Format price with user's currency symbol
   * @param price Price to format
   * @param options Formatting options
   * @returns Formatted price string
   */
  const formatPrice = useCallback((
    price: number,
    options: {
      showSymbol?: boolean;
      decimals?: number;
      locale?: string;
    } = {}
  ): string => {
    const userCountry = getUserCountry();
    return CurrencyService.formatPrice(price, userCountry, options);
  }, [getUserCountry]);

  /**
   * Convert and format USD price for display
   * @param usdPrice Price in USD
   * @param options Formatting options
   * @returns Promise with formatted price string
   */
  const convertAndFormatPrice = useCallback(async (
    usdPrice: number,
    options: {
      showSymbol?: boolean;
      decimals?: number;
      locale?: string;
    } = {}
  ): Promise<string> => {
    try {
      setIsLoading(true);
      setError(null);

      const userCountry = getUserCountry();
      const formattedPrice = await CurrencyService.convertAndFormatPrice(usdPrice, userCountry, options);

      return formattedPrice;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Currency conversion failed';
      setError(errorMessage);
      // Fallback to USD formatting
      return CurrencyService.formatPrice(usdPrice, 'US', options);
    } finally {
      setIsLoading(false);
    }
  }, [getUserCountry]);

  /**
   * Get user's currency information
   * @returns Currency info object
   */
  const getCurrencyInfo = useCallback(() => {
    const userCountry = getUserCountry();
    return CurrencyService.getCurrencyForCountry(userCountry);
  }, [getUserCountry]);

  /**
   * Get exchange rate for user's currency
   * @returns Promise with exchange rate
   */
  const getExchangeRate = useCallback(async (): Promise<number | null> => {
    try {
      const userCountry = getUserCountry();
      const currencyInfo = CurrencyService.getCurrencyForCountry(userCountry);
      return await CurrencyService.getExchangeRate(currencyInfo.currency);
    } catch (err) {
      console.error('Error getting exchange rate:', err);
      return null;
    }
  }, [getUserCountry]);

  return {
    convertPrice,
    formatPrice,
    convertAndFormatPrice,
    getCurrencyInfo,
    getExchangeRate,
    isLoading,
    error,
    userCountry: getUserCountry(),
  };
}

/**
 * Hook for converting multiple prices at once
 * @param usdPrices Array of USD prices
 * @returns Object with converted prices and loading state
 */
export function useCurrencyBatch(usdPrices: number[]) {
  const [convertedPrices, setConvertedPrices] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { convertPrice } = useCurrency();

  useEffect(() => {
    const convertPrices = async () => {
      if (usdPrices.length === 0) return;

      try {
        setIsLoading(true);
        setError(null);

        const converted = await Promise.all(
          usdPrices.map(price => convertPrice(price))
        );

        setConvertedPrices(converted);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Batch currency conversion failed';
        setError(errorMessage);
        setConvertedPrices(usdPrices); // Fallback to original prices
      } finally {
        setIsLoading(false);
      }
    };

    convertPrices();
  }, [usdPrices, convertPrice]);

  return {
    convertedPrices,
    isLoading,
    error,
  };
}

/**
 * Hook for real-time price display with currency conversion
 * @param usdPrice USD price to display
 * @returns Object with formatted price and loading state
 */
export function usePriceDisplay(usdPrice: number) {
  const [displayPrice, setDisplayPrice] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const { convertAndFormatPrice } = useCurrency();

  useEffect(() => {
    const updateDisplayPrice = async () => {
      try {
        setIsLoading(true);
        const formatted = await convertAndFormatPrice(usdPrice);
        setDisplayPrice(formatted);
      } catch (err) {
        console.error('Error updating display price:', err);
        // Fallback to USD formatting
        setDisplayPrice(CurrencyService.formatPrice(usdPrice, 'US'));
      } finally {
        setIsLoading(false);
      }
    };

    updateDisplayPrice();
  }, [usdPrice, convertAndFormatPrice]);

  return {
    displayPrice,
    isLoading,
  };
}
